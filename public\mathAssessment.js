/**
 * Mathematics Assessment Module
 * Handles the mathematics assessment flow for student users
 */

class MathAssessment {
  constructor() {
    this.currentLevel = 'Entry';
    this.timeLimit = 30 * 60; // Default 30 minutes in seconds
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;
    this.isSubmitted = false;
    
    // Assessment data
    this.questions = [];
    this.currentQuestionIndex = 0;
    this.answers = [];
    this.assessmentId = null;
    
    // Assessment metadata
    this.assessmentStartTime = null;
    this.questionStartTimes = [];

    // Performance tracking
    this.performanceData = {};
    this.interactionLogs = {};
    this.currentQuestionPerformance = null;
    
    // Level specifications
    this.levelSpecs = {
      'Entry': { timeLimit: 30 * 60, questionCount: 22, passingScore: 24, maxScore: 44 },
      'Level1': { timeLimit: 30 * 60, questionCount: 13, passingScore: 16, maxScore: 26 },
      'GCSEPart1': { timeLimit: 15 * 60, questionCount: 7, passingScore: 5, maxScore: 10 },
      'GCSEPart2': { timeLimit: 20 * 60, questionCount: 10, passingScore: 8, maxScore: 20 }
    };
  }

  /**
   * Initialize the mathematics assessment
   */
  async init() {
    this.setupEventListeners();
    this.initializeCalculator();
    console.log('Mathematics assessment initialized');

    // Auto-initialize if page is loaded directly
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.autoInit());
    } else {
      this.autoInit();
    }
  }

  /**
   * Auto-initialize the assessment interface
   */
  autoInit() {
    // Ensure proper initial state
    this.resetToInitialState();
  }

  /**
   * Reset to initial state - only user form visible
   */
  resetToInitialState() {
    // Hide all assessment containers
    const mathContainer = document.getElementById('math-assessment-container');
    const header = document.getElementById('header');

    if (mathContainer) mathContainer.classList.add('hidden');
    if (header) header.classList.add('hidden');

    // Hide all assessment screens
    this.hideAllAssessmentScreens();

    // Show only the user form
    const userFormContainer = document.getElementById('user-form-container');
    if (userFormContainer) {
      userFormContainer.classList.remove('hidden');
    }
  }

  /**
   * Setup event listeners for the assessment
   */
  setupEventListeners() {
    // Form submission
    const userForm = document.getElementById('user-form');
    if (userForm) {
      userForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
    }

    // Begin assessment button
    const beginBtn = document.getElementById('begin-assessment-btn');
    if (beginBtn) {
      beginBtn.addEventListener('click', () => this.beginAssessment());
    }

    // Next question button
    const nextBtn = document.getElementById('next-question-btn');
    if (nextBtn) {
      nextBtn.addEventListener('click', () => this.nextQuestion());
    }

    // Skip question button
    const skipBtn = document.getElementById('skip-question-btn');
    if (skipBtn) {
      skipBtn.addEventListener('click', () => this.skipQuestion());
    }

    // Answer option buttons
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
      btn.addEventListener('click', (e) => this.selectOption(e));
    });

    // Numeric input
    const numericInput = document.getElementById('numeric-answer');
    if (numericInput) {
      numericInput.addEventListener('input', () => this.handleNumericInput());
    }

    // Short answer input
    const shortAnswerInput = document.getElementById('short-answer');
    if (shortAnswerInput) {
      shortAnswerInput.addEventListener('input', () => this.handleShortAnswerInput());
    }

    // Modal event listeners
    this.setupModalEventListeners();
  }

  /**
   * Setup modal event listeners
   */
  setupModalEventListeners() {
    // Close modal buttons
    const closeModalBtn = document.getElementById('close-modal-btn');
    const closeModalFooterBtn = document.getElementById('close-modal-footer-btn');

    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', () => this.hideDetailedReportModal());
    }

    if (closeModalFooterBtn) {
      closeModalFooterBtn.addEventListener('click', () => this.hideDetailedReportModal());
    }



    // Close modal when clicking overlay
    const modalOverlay = document.getElementById('detailed-report-modal');
    if (modalOverlay) {
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          this.hideDetailedReportModal();
        }
      });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const modal = document.getElementById('detailed-report-modal');
        if (modal && !modal.classList.contains('hidden')) {
          this.hideDetailedReportModal();
        }
      }
    });
  }

  /**
   * Initialize calculator widget
   */
  initializeCalculator() {
    this.calculator = new Calculator();
    this.setupCalculatorEventListeners();
    this.setupCalculatorDragging();
    this.setupCalculatorScrolling();
  }

  /**
   * Setup calculator event listeners
   */
  setupCalculatorEventListeners() {
    const toggleBtn = document.getElementById('calculator-toggle-btn');
    const closeBtn = document.getElementById('calculator-close-btn');
    const modal = document.getElementById('calculator-modal');
    const header = document.querySelector('.calculator-header');

    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.showCalculator());
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideCalculator());
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideCalculator();
        }
      });
    }

    // Setup dragging on calculator header only
    if (header) {
      // Mouse events
      header.addEventListener('mousedown', (e) => this.startCalculatorDrag(e));

      // Touch events for mobile
      header.addEventListener('touchstart', (e) => this.startCalculatorDrag(e), { passive: false });
    }

    // Prevent dragging from scrollable section
    const scrollableSection = document.getElementById('calculator-scrollable');
    if (scrollableSection) {
      scrollableSection.addEventListener('mousedown', (e) => {
        e.stopPropagation();
      });

      scrollableSection.addEventListener('touchstart', (e) => {
        e.stopPropagation();
      }, { passive: true });
    }

    // Calculator button event listeners
    const calculatorButtons = document.querySelectorAll('.calc-btn');
    calculatorButtons.forEach(btn => {
      btn.addEventListener('click', (e) => this.handleCalculatorButton(e));
    });

    // Keyboard support for calculator
    document.addEventListener('keydown', (e) => this.handleCalculatorKeyboard(e));
  }

  /**
   * Show calculator widget
   */
  showCalculator() {
    const modal = document.getElementById('calculator-modal');
    const container = document.getElementById('calculator-container');
    const toggleBtn = document.getElementById('calculator-toggle-btn');

    if (modal && container) {
      modal.classList.remove('hidden');
      modal.classList.add('show');
      container.classList.add('show');

      // Set up accessibility attributes
      container.setAttribute('tabindex', '0');
      container.setAttribute('role', 'dialog');
      container.setAttribute('aria-modal', 'true');
      container.setAttribute('aria-labelledby', 'calculator-title');
      container.setAttribute('aria-describedby', 'calculator-help');

      // Add help text for screen readers
      this.addCalculatorHelpText();

      // Store the element that opened the calculator for focus restoration
      this.calculatorOpener = toggleBtn;

      // Focus on calculator for keyboard navigation
      setTimeout(() => {
        container.focus();
      }, 100);

      // Announce calculator opening with help information
      this.announceToScreenReader('Scientific calculator opened. Press H for keyboard shortcuts help.');
    }
  }

  /**
   * Add help text for screen readers
   */
  addCalculatorHelpText() {
    let helpText = document.getElementById('calculator-help');
    if (!helpText) {
      helpText = document.createElement('div');
      helpText.id = 'calculator-help';
      helpText.className = 'sr-only';
      helpText.innerHTML = `
        <p>Scientific calculator with keyboard shortcuts:</p>
        <ul>
          <li>Numbers 0-9: Input digits</li>
          <li>+, -, *, /: Basic operations</li>
          <li>^: Power operation</li>
          <li>(, ): Parentheses</li>
          <li>S: Sine, C: Cosine, T: Tangent</li>
          <li>L: Log base 10, N: Natural log</li>
          <li>Q: Square root, R: Cube root</li>
          <li>P: Pi constant, E: Euler's number</li>
          <li>%: Percentage, !: Factorial</li>
          <li>A: Absolute value</li>
          <li>Ctrl+M: Memory add, Ctrl+R: Memory recall, Ctrl+L: Memory clear</li>
          <li>Enter or =: Calculate result</li>
          <li>Backspace: Delete last digit</li>
          <li>Delete: Clear all</li>
          <li>Escape: Close calculator</li>
        </ul>
      `;
      document.body.appendChild(helpText);
    }
  }

  /**
   * Hide calculator widget
   */
  hideCalculator() {
    const modal = document.getElementById('calculator-modal');
    const container = document.getElementById('calculator-container');

    if (modal && container) {
      modal.classList.add('hide');

      // Announce calculator closing to screen readers
      this.announceToScreenReader('Calculator closed');

      setTimeout(() => {
        modal.classList.add('hidden');
        modal.classList.remove('show', 'hide');
        container.classList.remove('show');

        // Remove accessibility attributes
        container.removeAttribute('tabindex');
        container.removeAttribute('role');
        container.removeAttribute('aria-modal');
        container.removeAttribute('aria-labelledby');

        // Restore focus to the element that opened the calculator
        if (this.calculatorOpener) {
          this.calculatorOpener.focus();
          this.calculatorOpener = null;
        }
      }, 200);
    }
  }

  /**
   * Handle calculator button clicks
   */
  handleCalculatorButton(e) {
    const btn = e.target;

    // Add pressed animation
    btn.classList.add('pressed');
    setTimeout(() => btn.classList.remove('pressed'), 100);

    // Handle different button types
    if (btn.dataset.number) {
      this.calculator.inputNumber(btn.dataset.number);
    } else if (btn.dataset.operation) {
      this.calculator.inputOperation(btn.dataset.operation);
    } else if (btn.dataset.function) {
      // Handle scientific functions
      if (btn.dataset.function === 'power') {
        // Special handling for power function (x^y)
        this.calculator.inputOperation('^');
      } else {
        this.calculator.performFunction(btn.dataset.function);
      }
    } else if (btn.dataset.action) {
      // Handle special actions including memory and parentheses
      if (btn.dataset.action.startsWith('memory-')) {
        this.calculator.performMemoryAction(btn.dataset.action);
      } else if (btn.dataset.action === 'parenthesis-open') {
        this.calculator.handleParenthesis('open');
      } else if (btn.dataset.action === 'parenthesis-close') {
        this.calculator.handleParenthesis('close');
      } else {
        this.calculator.performAction(btn.dataset.action);
      }
    }

    this.updateCalculatorDisplay();
  }

  /**
   * Handle calculator keyboard input
   */
  handleCalculatorKeyboard(e) {
    // Only handle keyboard input when calculator is visible
    const modal = document.getElementById('calculator-modal');
    if (!modal || modal.classList.contains('hidden')) {
      return;
    }

    const key = e.key;

    // Handle Tab navigation within calculator
    if (key === 'Tab') {
      this.handleCalculatorTabNavigation(e);
      return;
    }

    // Prevent default for calculator keys (including scientific function shortcuts)
    if (/[0-9+\-*/.=()^%!]/.test(key) || key === 'Enter' || key === 'Backspace' || key === 'Delete' || key === 'Escape' ||
        /[slpecqrtfamn]/.test(key.toLowerCase())) {
      e.preventDefault();
    }

    // Handle number keys
    if (/[0-9]/.test(key)) {
      this.calculator.inputNumber(key);
      this.announceToScreenReader(`${key} pressed`);
    }
    // Handle operation keys
    else if (key === '+') {
      this.calculator.inputOperation('+');
      this.announceToScreenReader('Plus');
    } else if (key === '-') {
      this.calculator.inputOperation('-');
      this.announceToScreenReader('Minus');
    } else if (key === '*') {
      this.calculator.inputOperation('*');
      this.announceToScreenReader('Multiply');
    } else if (key === '/') {
      this.calculator.inputOperation('/');
      this.announceToScreenReader('Divide');
    }
    // Handle decimal point
    else if (key === '.') {
      this.calculator.performAction('decimal');
      this.announceToScreenReader('Decimal point');
    }
    // Handle equals
    else if (key === '=' || key === 'Enter') {
      this.calculator.performAction('equals');
      this.announceToScreenReader('Equals');
    }
    // Handle backspace
    else if (key === 'Backspace') {
      this.calculator.performAction('backspace');
      this.announceToScreenReader('Backspace');
    }
    // Handle clear
    else if (key === 'Delete') {
      this.calculator.performAction('clear');
      this.announceToScreenReader('Clear');
    }
    // Handle parentheses
    else if (key === '(') {
      this.calculator.handleParenthesis('open');
      this.announceToScreenReader('Open parenthesis');
    } else if (key === ')') {
      this.calculator.handleParenthesis('close');
      this.announceToScreenReader('Close parenthesis');
    }
    // Handle power
    else if (key === '^') {
      this.calculator.inputOperation('^');
      this.announceToScreenReader('Power');
    }
    // Handle percentage
    else if (key === '%') {
      this.calculator.performFunction('percentage');
      this.announceToScreenReader('Percentage');
    }
    // Handle factorial
    else if (key === '!') {
      this.calculator.performFunction('factorial');
      this.announceToScreenReader('Factorial');
    }
    // Handle scientific function shortcuts
    else if (key.toLowerCase() === 's') {
      this.calculator.performFunction('sin');
      this.announceToScreenReader('Sine');
    } else if (key.toLowerCase() === 'c') {
      this.calculator.performFunction('cos');
      this.announceToScreenReader('Cosine');
    } else if (key.toLowerCase() === 't') {
      this.calculator.performFunction('tan');
      this.announceToScreenReader('Tangent');
    } else if (key.toLowerCase() === 'l') {
      this.calculator.performFunction('log');
      this.announceToScreenReader('Logarithm');
    } else if (key.toLowerCase() === 'n') {
      this.calculator.performFunction('ln');
      this.announceToScreenReader('Natural logarithm');
    } else if (key.toLowerCase() === 'q') {
      this.calculator.performFunction('sqrt');
      this.announceToScreenReader('Square root');
    } else if (key.toLowerCase() === 'r') {
      this.calculator.performFunction('cbrt');
      this.announceToScreenReader('Cube root');
    } else if (key.toLowerCase() === 'p') {
      this.calculator.performFunction('pi');
      this.announceToScreenReader('Pi');
    } else if (key.toLowerCase() === 'e') {
      this.calculator.performFunction('e');
      this.announceToScreenReader('Euler number');
    } else if (key.toLowerCase() === 'f') {
      this.calculator.performFunction('factorial');
      this.announceToScreenReader('Factorial');
    } else if (key.toLowerCase() === 'a') {
      this.calculator.performFunction('abs');
      this.announceToScreenReader('Absolute value');
    } else if (key.toLowerCase() === 'h') {
      this.announceCalculatorHelp();
    }
    // Handle memory shortcuts (Ctrl + key combinations)
    else if (e.ctrlKey) {
      if (key.toLowerCase() === 'm') {
        this.calculator.performMemoryAction('memory-add');
        this.announceToScreenReader('Memory add');
      } else if (key.toLowerCase() === 'r') {
        this.calculator.performMemoryAction('memory-recall');
        this.announceToScreenReader('Memory recall');
      } else if (key.toLowerCase() === 'l') {
        this.calculator.performMemoryAction('memory-clear');
        this.announceToScreenReader('Memory clear');
      }
    }
    // Handle escape to close calculator
    else if (key === 'Escape') {
      this.hideCalculator();
      return;
    }

    this.updateCalculatorDisplay();
  }

  /**
   * Handle Tab navigation within calculator
   */
  handleCalculatorTabNavigation(e) {
    const container = document.getElementById('calculator-container');
    if (!container) return;

    const focusableElements = container.querySelectorAll(
      'button, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (e.shiftKey) {
      // Shift + Tab (backward)
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab (forward)
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }

  /**
   * Update calculator display
   */
  updateCalculatorDisplay() {
    const screen = document.getElementById('calculator-screen');
    if (screen) {
      const displayValue = this.calculator.getDisplayValue();
      screen.textContent = displayValue;

      // Create more descriptive aria-label for mathematical expressions
      const accessibleValue = this.makeValueAccessible(displayValue);
      screen.setAttribute('aria-label', `Calculator display: ${accessibleValue}`);

      // Handle error state
      if (this.calculator.hasError()) {
        screen.classList.add('error');
        screen.setAttribute('aria-label', 'Calculator display: Error - Invalid operation or result');
        this.announceToScreenReader('Calculator error: Invalid operation or result');
        setTimeout(() => screen.classList.remove('error'), 1000);
      }
    }
  }

  /**
   * Make calculator display value more accessible for screen readers
   */
  makeValueAccessible(value) {
    if (value === 'Math ERROR') {
      return 'Math Error - Invalid operation or result';
    }

    // Handle Casio-style scientific notation
    if (value.includes('E')) {
      const parts = value.split('E');
      const mantissa = parts[0];
      const exponent = parts[1];
      return `${mantissa} times 10 to the power of ${exponent}`;
    }

    // Handle scientific notation with lowercase e
    if (value.includes('e')) {
      const parts = value.split('e');
      const mantissa = parts[0];
      const exponent = parts[1];
      return `${mantissa} times 10 to the power of ${exponent}`;
    }

    // Handle very long decimal numbers
    if (value.includes('.') && value.length > 10) {
      const decimalPlaces = value.split('.')[1].length;
      return `${value} (${decimalPlaces} decimal places)`;
    }

    // Handle special mathematical constants
    if (value === String(Math.PI)) {
      return `Pi (approximately ${value})`;
    }
    if (value === String(Math.E)) {
      return `Euler's number (approximately ${value})`;
    }

    return value;
  }

  /**
   * Announce message to screen readers
   */
  announceToScreenReader(message) {
    // Create or update live region for screen reader announcements
    let liveRegion = document.getElementById('calculator-live-region');
    if (!liveRegion) {
      liveRegion = document.createElement('div');
      liveRegion.id = 'calculator-live-region';
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.style.position = 'absolute';
      liveRegion.style.left = '-10000px';
      liveRegion.style.width = '1px';
      liveRegion.style.height = '1px';
      liveRegion.style.overflow = 'hidden';
      document.body.appendChild(liveRegion);
    }

    // Clear and set new message
    liveRegion.textContent = '';
    setTimeout(() => {
      liveRegion.textContent = message;
    }, 100);
  }

  /**
   * Announce calculator help information
   */
  announceCalculatorHelp() {
    const helpMessage = `Scientific calculator keyboard shortcuts:
      Numbers 0 through 9 for digits.
      Plus, minus, multiply, divide for basic operations.
      Caret for power.
      Parentheses for grouping.
      S for sine, C for cosine, T for tangent.
      L for log, N for natural log.
      Q for square root, R for cube root.
      P for pi, E for Euler's number.
      Percent for percentage, exclamation for factorial.
      A for absolute value.
      Control M for memory add, Control R for memory recall, Control L for memory clear.
      Enter or equals for result.
      Backspace to delete, Delete to clear all.
      Escape to close calculator.`;

    this.announceToScreenReader(helpMessage);
  }

  /**
   * Show calculator widget
   */
  showCalculatorWidget() {
    const widget = document.getElementById('calculator-widget');
    if (widget) {
      widget.classList.remove('hidden');
    }
  }

  /**
   * Hide calculator widget
   */
  hideCalculatorWidget() {
    const widget = document.getElementById('calculator-widget');
    const modal = document.getElementById('calculator-modal');

    if (widget) {
      widget.classList.add('hidden');
    }

    // Also hide the calculator modal if it's open
    if (modal && !modal.classList.contains('hidden')) {
      this.hideCalculator();
    }
  }

  /**
   * Setup calculator dragging functionality
   */
  setupCalculatorDragging() {
    this.dragState = {
      isDragging: false,
      startX: 0,
      startY: 0,
      initialX: 0,
      initialY: 0
    };
  }

  /**
   * Start dragging calculator
   */
  startCalculatorDrag(e) {
    const container = document.getElementById('calculator-container');
    if (!container) return;

    this.dragState.isDragging = true;
    container.classList.add('dragging');

    // Get initial positions
    const rect = container.getBoundingClientRect();
    this.dragState.initialX = rect.left;
    this.dragState.initialY = rect.top;

    // Get mouse/touch position
    const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
    const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;

    this.dragState.startX = clientX - this.dragState.initialX;
    this.dragState.startY = clientY - this.dragState.initialY;

    // Add event listeners for drag and end
    if (e.type.includes('touch')) {
      document.addEventListener('touchmove', this.handleCalculatorDrag.bind(this), { passive: false });
      document.addEventListener('touchend', this.endCalculatorDrag.bind(this));
    } else {
      document.addEventListener('mousemove', this.handleCalculatorDrag.bind(this));
      document.addEventListener('mouseup', this.endCalculatorDrag.bind(this));
    }

    e.preventDefault();
  }

  /**
   * Handle calculator dragging
   */
  handleCalculatorDrag(e) {
    if (!this.dragState.isDragging) return;

    const container = document.getElementById('calculator-container');
    if (!container) return;

    // Get current mouse/touch position
    const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
    const clientY = e.type.includes('touch') ? e.touches[0].clientY : e.clientY;

    // Calculate new position
    let newX = clientX - this.dragState.startX;
    let newY = clientY - this.dragState.startY;

    // Constrain to viewport
    const containerRect = container.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    newX = Math.max(0, Math.min(newX, viewportWidth - containerRect.width));
    newY = Math.max(0, Math.min(newY, viewportHeight - containerRect.height));

    // Apply new position
    container.style.position = 'fixed';
    container.style.left = `${newX}px`;
    container.style.top = `${newY}px`;
    container.style.transform = 'none';

    e.preventDefault();
  }

  /**
   * End calculator dragging
   */
  endCalculatorDrag() {
    const container = document.getElementById('calculator-container');
    if (container) {
      container.classList.remove('dragging');
    }

    this.dragState.isDragging = false;

    // Remove event listeners
    document.removeEventListener('mousemove', this.handleCalculatorDrag.bind(this));
    document.removeEventListener('mouseup', this.endCalculatorDrag.bind(this));
    document.removeEventListener('touchmove', this.handleCalculatorDrag.bind(this));
    document.removeEventListener('touchend', this.endCalculatorDrag.bind(this));
  }

  /**
   * Setup calculator scrolling functionality
   */
  setupCalculatorScrolling() {
    const scrollableSection = document.getElementById('calculator-scrollable');
    if (!scrollableSection) return;

    // Add scroll event listener for scroll indicators
    scrollableSection.addEventListener('scroll', () => {
      this.updateScrollIndicators();
    });

    // Optimize touch scrolling performance
    this.optimizeTouchScrolling(scrollableSection);

    // Initial scroll indicator update
    setTimeout(() => {
      this.updateScrollIndicators();
    }, 100);

    // Update scroll indicators when calculator is shown
    const observer = new MutationObserver(() => {
      if (!document.getElementById('calculator-modal').classList.contains('hidden')) {
        setTimeout(() => {
          this.updateScrollIndicators();
        }, 100);
      }
    });

    observer.observe(document.getElementById('calculator-modal'), {
      attributes: true,
      attributeFilter: ['class']
    });
  }

  /**
   * Optimize touch scrolling for mobile devices
   */
  optimizeTouchScrolling(scrollableSection) {
    let isScrolling = false;
    let scrollTimeout;

    // Add touch-friendly scroll momentum
    scrollableSection.addEventListener('touchstart', () => {
      isScrolling = true;
      clearTimeout(scrollTimeout);
    }, { passive: true });

    scrollableSection.addEventListener('touchend', () => {
      scrollTimeout = setTimeout(() => {
        isScrolling = false;
      }, 150);
    }, { passive: true });

    // Smooth scroll behavior for programmatic scrolling
    scrollableSection.addEventListener('wheel', (e) => {
      if (Math.abs(e.deltaY) > 0) {
        e.preventDefault();
        const scrollAmount = e.deltaY * 0.5; // Reduce scroll speed for better control
        scrollableSection.scrollBy({
          top: scrollAmount,
          behavior: 'smooth'
        });
      }
    }, { passive: false });

    // Ensure buttons remain responsive during scrolling
    scrollableSection.addEventListener('scroll', () => {
      if (!isScrolling) {
        // Update scroll indicators with throttling
        clearTimeout(this.scrollIndicatorTimeout);
        this.scrollIndicatorTimeout = setTimeout(() => {
          this.updateScrollIndicators();
        }, 16); // ~60fps
      }
    }, { passive: true });
  }

  /**
   * Update scroll indicators based on scroll position
   */
  updateScrollIndicators() {
    const scrollableSection = document.getElementById('calculator-scrollable');
    const topIndicator = document.getElementById('scroll-indicator-top');
    const bottomIndicator = document.getElementById('scroll-indicator-bottom');

    if (!scrollableSection || !topIndicator || !bottomIndicator) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollableSection;
    const isScrollable = scrollHeight > clientHeight;

    if (!isScrollable) {
      // No scrolling needed, hide indicators
      topIndicator.classList.remove('visible');
      bottomIndicator.classList.remove('visible');
      return;
    }

    // Show top indicator if not at top
    if (scrollTop > 10) {
      topIndicator.classList.add('visible');
    } else {
      topIndicator.classList.remove('visible');
    }

    // Show bottom indicator if not at bottom
    if (scrollTop < scrollHeight - clientHeight - 10) {
      bottomIndicator.classList.add('visible');
    } else {
      bottomIndicator.classList.remove('visible');
    }
  }

  /**
   * Handle form submission to start assessment
   */
  async handleFormSubmit(e) {
    e.preventDefault();
    
    try {
      // Get form data
      const formData = new FormData(e.target);
      const firstName = formData.get('first-name');
      const lastName = formData.get('last-name');
      const email = formData.get('email');
      const assessmentLevel = formData.get('assessment-level');
      const studentLevel = formData.get('student-level');

      // Validate required fields
      if (!firstName || !lastName || !email || !assessmentLevel || !studentLevel) {
        alert('Please fill in all required fields');
        return;
      }

      // Store user data
      this.userData = {
        firstName,
        lastName,
        email,
        assessmentLevel,
        studentLevel,
        name: `${firstName} ${lastName}`
      };

      this.currentLevel = assessmentLevel;
      
      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;

      // Hide form and show instructions
      this.hideUserForm();
      this.showInstructions();

    } catch (error) {
      console.error('Error handling form submission:', error);
      alert('An error occurred. Please try again.');
    }
  }

  /**
   * Show assessment instructions
   */
  showInstructions() {
    const specs = this.levelSpecs[this.currentLevel];

    // Ensure user form is completely hidden
    this.hideUserForm();

    // Hide all assessment screens first
    this.hideAllAssessmentScreens();

    // Update instruction display
    document.getElementById('time-limit-display').textContent = `${specs.timeLimit / 60} minutes`;
    document.getElementById('question-count-display').textContent = specs.questionCount;
    document.getElementById('passing-score-display').textContent = `${specs.passingScore} points`;

    // Update header
    document.getElementById('current-level').textContent = this.currentLevel;
    document.getElementById('total-questions').textContent = specs.questionCount;

    // Show only the math assessment container and instructions
    document.getElementById('math-assessment-container').classList.remove('hidden');
    document.getElementById('assessment-instructions').classList.remove('hidden');
    document.getElementById('header').classList.remove('hidden');

    // Hide calculator widget during instructions
    this.hideCalculatorWidget();
  }

  /**
   * Begin the assessment
   */
  async beginAssessment() {
    try {
      // Show loading with progressive messages
      this.showProgressiveLoading();

      // Start assessment
      await this.startAssessment();

      // Hide loading and show questions
      this.hideLoading();
      this.showQuestions();

    } catch (error) {
      console.error('Error beginning assessment:', error);
      this.hideLoading();
      alert('Failed to start assessment. Please try again.');
    }
  }

  /**
   * Show progressive loading with dynamic messages
   */
  showProgressiveLoading() {
    const messages = [
      'Personalising your assessment...',
      'Preparing questions for your level...',
      'Optimizing difficulty settings...',
      'Finalizing your mathematics assessment...'
    ];

    let currentMessageIndex = 0;

    // Show initial loading
    this.showLoading(messages[currentMessageIndex]);

    // Change messages progressively
    this.loadingMessageInterval = setInterval(() => {
      currentMessageIndex++;
      if (currentMessageIndex < messages.length) {
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
          // Fade out current message
          loadingText.style.opacity = '0.5';

          setTimeout(() => {
            loadingText.textContent = messages[currentMessageIndex];
            loadingText.style.opacity = '1';
          }, 300);
        }
      } else {
        clearInterval(this.loadingMessageInterval);
      }
    }, 1200); // Change message every 1.2 seconds
  }

  /**
   * Start the assessment by fetching questions and mixing with interactive questions
   */
  async startAssessment() {
    try {
      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3003'
        : window.location.origin;

      // Update progress: Starting request
      this.updateLoadingProgress(25, 'Connecting to assessment server...');

      // Try to warm cache for next level in background
      this.preloadNextLevelQuestions();

      const response = await fetch(`${baseUrl}/api/math-assessments/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: this.currentLevel,
          email: this.userData.email,
          studentLevel: this.userData.studentLevel
        }),
      });

      // Update progress: Request sent
      this.updateLoadingProgress(50, 'Processing your assessment level...');

      if (!response.ok) {
        throw new Error(`Failed to start assessment: ${response.status}`);
      }

      const data = await response.json();

      // Update progress: Data received
      this.updateLoadingProgress(75, 'Preparing your questions...');

      this.assessmentId = data.assessmentId;

      // Mix AI-generated questions with interactive questions
      this.questions = this.mixQuestionsWithInteractive(data.questions);

      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentStartTime = new Date();

      // Update progress: Almost complete
      this.updateLoadingProgress(90, 'Setting up your assessment...');

      // Start timer
      this.startTimer();

      // Final progress update
      this.updateLoadingProgress(100, 'Assessment ready!');

      console.log('Assessment started:', {
        assessmentId: this.assessmentId,
        level: this.currentLevel,
        questionCount: this.questions.length,
        interactiveCount: this.questions.filter(q => this.isInteractiveQuestion(q)).length
      });

      // Small delay to show completion
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      console.error('Error starting assessment:', error);
      throw error;
    }
  }

  /**
   * Mix AI-generated questions with pre-loaded interactive questions
   */
  mixQuestionsWithInteractive(aiQuestions) {
    // Get interactive questions for this level
    const interactiveQuestions = window.InteractiveQuestionData ?
      window.InteractiveQuestionData.getQuestions(this.currentLevel) : [];

    console.log('Mixing questions:', {
      aiQuestions: aiQuestions.length,
      interactiveQuestions: interactiveQuestions.length
    });

    // If no interactive questions available, return AI questions only
    if (interactiveQuestions.length === 0) {
      return aiQuestions;
    }

    // Calculate how many interactive questions to include (20-30% of total)
    const totalQuestions = aiQuestions.length;
    const interactiveCount = Math.min(
      Math.ceil(totalQuestions * 0.25), // 25% of total
      interactiveQuestions.length
    );

    // Randomly select interactive questions
    const selectedInteractive = this.shuffleArray([...interactiveQuestions])
      .slice(0, interactiveCount);

    // Mix questions together
    const mixedQuestions = [...aiQuestions];

    // Insert interactive questions at regular intervals
    const interval = Math.floor(totalQuestions / interactiveCount);
    selectedInteractive.forEach((interactiveQ, index) => {
      const insertPosition = (index + 1) * interval;
      if (insertPosition < mixedQuestions.length) {
        mixedQuestions.splice(insertPosition, 0, interactiveQ);
      } else {
        mixedQuestions.push(interactiveQ);
      }
    });

    console.log('Questions mixed successfully:', {
      total: mixedQuestions.length,
      interactive: selectedInteractive.length,
      ai: aiQuestions.length
    });

    return mixedQuestions;
  }

  /**
   * Check if a question is an interactive type
   */
  isInteractiveQuestion(question) {
    const interactiveTypes = [
      'number-line', 'drag-drop', 'visual-calculator', 'number-bonds', 'step-by-step',
      'coordinate-plot', 'ratio-slider', 'equation-builder', 'pattern-completion'
    ];
    return interactiveTypes.includes(question.type);
  }

  /**
   * Shuffle array using Fisher-Yates algorithm
   */
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Set up fallback activation for interactive questions
   */
  setupFallbackActivation(question) {
    // Add completion indicator
    this.addCompletionIndicator(question.type);

    // Add question status indicator
    this.addQuestionStatusIndicator();

    // Enable next button after 30 seconds if no interaction
    this.fallbackTimer = setTimeout(() => {
      const nextBtn = document.getElementById('next-question-btn');
      if (nextBtn && nextBtn.disabled) {
        nextBtn.disabled = false;
        this.updateQuestionStatus('timeout', 'Question timeout - you can proceed');
        console.log('Fallback activation: Next button enabled after timeout');
      }
    }, 30000); // 30 seconds
  }

  /**
   * Clear fallback timer
   */
  clearFallbackTimer() {
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
    }
  }

  /**
   * Add completion indicator to interactive question
   */
  addCompletionIndicator(questionType) {
    const container = document.getElementById(this.getContainerIdForType(questionType));
    if (!container) return;

    // Remove existing indicator
    const existing = container.querySelector('.completion-indicator');
    if (existing) existing.remove();

    // Add new indicator
    const indicator = document.createElement('div');
    indicator.className = 'completion-indicator';
    indicator.textContent = '✓ Complete';
    container.appendChild(indicator);
  }

  /**
   * Add question status indicator
   */
  addQuestionStatusIndicator() {
    const questionContainer = document.querySelector('.question-container') ||
                            document.querySelector('.interactive-question:not(.hidden)');

    if (!questionContainer) return;

    // Remove existing status
    const existing = questionContainer.querySelector('.question-status');
    if (existing) existing.remove();

    // Add status indicator
    const statusDiv = document.createElement('div');
    statusDiv.className = 'question-status';
    statusDiv.innerHTML = `
      <div class="status-indicator active"></div>
      <span class="status-text">Interact with the question above</span>
    `;

    questionContainer.appendChild(statusDiv);
  }

  /**
   * Update question status
   */
  updateQuestionStatus(status, message) {
    const statusIndicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-text');

    if (statusIndicator && statusText) {
      statusIndicator.className = `status-indicator ${status}`;
      statusText.textContent = message;
    }
  }

  /**
   * Show completion indicator
   */
  showCompletionIndicator() {
    const indicator = document.querySelector('.completion-indicator');
    if (indicator) {
      indicator.classList.add('show');
    }

    this.updateQuestionStatus('complete', 'Question completed - ready to proceed');
  }

  /**
   * Get container ID for question type
   */
  getContainerIdForType(type) {
    const typeMap = {
      'number-line': 'number-line-slider',
      'drag-drop': 'drag-drop-matching',
      'visual-calculator': 'visual-calculator',
      'number-bonds': 'number-bonds',
      'step-by-step': 'step-by-step-guided',
      'coordinate-plot': 'coordinate-plotting',
      'ratio-slider': 'ratio-sliders',
      'equation-builder': 'equation-builders',
      'pattern-completion': 'pattern-completion'
    };
    return typeMap[type];
  }

  /**
   * Update loading progress indicator
   */
  updateLoadingProgress(percentage, message) {
    const progressFill = document.querySelector('.progress-fill');
    const loadingText = document.querySelector('.loading-text');

    if (progressFill) {
      progressFill.style.width = `${percentage}%`;
      progressFill.style.animation = 'none'; // Stop the infinite animation
    }

    if (loadingText && message) {
      loadingText.style.opacity = '0.5';
      setTimeout(() => {
        loadingText.textContent = message;
        loadingText.style.opacity = '1';
      }, 150);
    }
  }

  /**
   * Preload questions for next level in background
   */
  async preloadNextLevelQuestions() {
    try {
      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3003'
        : window.location.origin;

      // Determine next level for preloading
      const levelProgression = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
      const currentIndex = levelProgression.indexOf(this.currentLevel);
      const nextLevel = currentIndex < levelProgression.length - 1 ? levelProgression[currentIndex + 1] : null;

      if (nextLevel) {
        console.log(`🚀 Preloading questions for next level: ${nextLevel}`);

        // Start background cache warming (non-blocking)
        fetch(`${baseUrl}/api/math-assessments/cache/warm`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            levels: [nextLevel],
            background: true
          }),
        }).catch(error => {
          console.warn('Background cache warming failed:', error);
        });
      }
    } catch (error) {
      console.warn('Error in preloading:', error);
    }
  }

  /**
   * Hide all assessment screens
   */
  hideAllAssessmentScreens() {
    document.getElementById('assessment-instructions').classList.add('hidden');
    document.getElementById('assessment-questions').classList.add('hidden');
    document.getElementById('assessment-results').classList.add('hidden');
    document.getElementById('assessment-loading').classList.add('hidden');
  }

  /**
   * Show the questions interface
   */
  showQuestions() {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show only questions container
    document.getElementById('assessment-questions').classList.remove('hidden');

    // Show calculator widget only for GCSE Part 2 (Calculator)
    if (this.currentLevel === 'GCSEPart2') {
      this.showCalculatorWidget();
    } else {
      this.hideCalculatorWidget();
    }

    // Load first question
    this.loadCurrentQuestion();
  }

  /**
   * Load the current question
   */
  loadCurrentQuestion() {
    if (this.currentQuestionIndex >= this.questions.length) {
      this.completeAssessment();
      return;
    }

    const question = this.questions[this.currentQuestionIndex];
    const specs = this.levelSpecs[this.currentLevel];
    
    // Update progress
    document.getElementById('question-number').textContent = this.currentQuestionIndex + 1;
    document.getElementById('total-question-count').textContent = specs.questionCount;
    document.getElementById('current-question').textContent = this.currentQuestionIndex + 1;
    
    const progressPercent = ((this.currentQuestionIndex + 1) / specs.questionCount) * 100;
    document.getElementById('progress-fill').style.width = `${progressPercent}%`;
    
    // Update question content
    document.getElementById('question-topic').textContent = question.topic || 'Mathematics';
    document.getElementById('question-text').textContent = question.question;
    
    // Hide all answer types
    this.hideAllAnswerTypes();
    
    // Show appropriate answer type
    if (question.type === 'multiple-choice') {
      this.showMultipleChoice(question);
    } else if (question.type === 'numeric') {
      this.showNumericInput();
    } else if (question.type === 'short-answer') {
      this.showShortAnswerInput();
    } else if (question.type === 'number-line') {
      this.showNumberLineSlider(question);
    } else if (question.type === 'drag-drop') {
      this.showDragDropMatching(question);
    } else if (question.type === 'visual-calculator') {
      this.showVisualCalculator(question);
    } else if (question.type === 'number-bonds') {
      this.showNumberBonds(question);
    } else if (question.type === 'step-by-step') {
      this.showStepByStepGuided(question);
    } else if (question.type === 'coordinate-plot') {
      this.showCoordinatePlotting(question);
    } else if (question.type === 'ratio-slider') {
      this.showRatioSliders(question);
    } else if (question.type === 'equation-builder') {
      this.showEquationBuilders(question);
    } else if (question.type === 'pattern-completion') {
      this.showPatternCompletion(question);
    } else {
      // Handle unknown question types gracefully
      console.error(`Unknown question type: ${question.type}. Defaulting to short-answer.`);
      this.showShortAnswerInput();
    }

    // Set up fallback activation for interactive questions
    if (this.isInteractiveQuestion(question)) {
      this.setupFallbackActivation(question);
    }
    
    // Reset next button
    document.getElementById('next-question-btn').disabled = true;
    
    // Record question start time and initialize performance tracking
    this.questionStartTimes[this.currentQuestionIndex] = new Date();
    this.initializeQuestionPerformanceTracking(question);
  }

  /**
   * Initialize performance tracking for current question
   */
  initializeQuestionPerformanceTracking(question) {
    const questionId = question.id;
    const questionIndex = this.currentQuestionIndex;

    this.currentQuestionPerformance = {
      questionId: questionId,
      questionType: question.type,
      topic: question.topic,
      startTime: new Date(),
      firstInteractionTime: null,
      interactions: [],
      attempts: 0,
      hintsUsed: 0,
      errorsCount: 0,
      completionTime: null,
      isComplete: false,
      solutionPath: [],
      difficultyIndicators: {
        timeToFirstInteraction: null,
        totalInteractions: 0,
        errorRate: 0,
        hintDependency: false,
        completionEfficiency: 0
      }
    };

    this.performanceData[questionIndex] = this.currentQuestionPerformance;
    this.interactionLogs[questionIndex] = [];

    console.log('Performance tracking initialized for question:', questionId);
  }

  /**
   * Log user interaction for performance analysis
   */
  logInteraction(interactionType, details = {}) {
    if (!this.currentQuestionPerformance) return;

    const timestamp = new Date();
    const interaction = {
      type: interactionType,
      timestamp: timestamp,
      timeFromStart: timestamp - this.currentQuestionPerformance.startTime,
      details: details
    };

    // Record first interaction time
    if (!this.currentQuestionPerformance.firstInteractionTime) {
      this.currentQuestionPerformance.firstInteractionTime = timestamp;
      this.currentQuestionPerformance.difficultyIndicators.timeToFirstInteraction =
        timestamp - this.currentQuestionPerformance.startTime;
    }

    // Add to interaction logs
    this.currentQuestionPerformance.interactions.push(interaction);
    this.interactionLogs[this.currentQuestionIndex].push(interaction);
    this.currentQuestionPerformance.difficultyIndicators.totalInteractions++;

    // Track specific interaction types
    switch (interactionType) {
      case 'attempt':
        this.currentQuestionPerformance.attempts++;
        break;
      case 'hint_request':
        this.currentQuestionPerformance.hintsUsed++;
        this.currentQuestionPerformance.difficultyIndicators.hintDependency = true;
        break;
      case 'error':
        this.currentQuestionPerformance.errorsCount++;
        break;
      case 'solution_step':
        this.currentQuestionPerformance.solutionPath.push(details);
        break;
    }

    // Update error rate
    if (this.currentQuestionPerformance.attempts > 0) {
      this.currentQuestionPerformance.difficultyIndicators.errorRate =
        this.currentQuestionPerformance.errorsCount / this.currentQuestionPerformance.attempts;
    }

    console.log('Interaction logged:', interactionType, details);
  }

  /**
   * Mark question as complete and finalize performance data
   */
  finalizeQuestionPerformance(isCorrect = true) {
    if (!this.currentQuestionPerformance) return;

    const completionTime = new Date();
    this.currentQuestionPerformance.completionTime = completionTime;
    this.currentQuestionPerformance.isComplete = true;

    const totalTime = completionTime - this.currentQuestionPerformance.startTime;

    // Calculate completion efficiency (lower is better)
    // Based on time, attempts, and errors
    const baseTime = 60000; // 1 minute baseline
    const timeEfficiency = Math.min(baseTime / totalTime, 1);
    const attemptEfficiency = Math.min(1 / Math.max(this.currentQuestionPerformance.attempts, 1), 1);
    const errorEfficiency = Math.max(1 - this.currentQuestionPerformance.difficultyIndicators.errorRate, 0);

    this.currentQuestionPerformance.difficultyIndicators.completionEfficiency =
      (timeEfficiency + attemptEfficiency + errorEfficiency) / 3;

    // Log completion
    this.logInteraction('completion', {
      isCorrect: isCorrect,
      totalTime: totalTime,
      efficiency: this.currentQuestionPerformance.difficultyIndicators.completionEfficiency
    });

    console.log('Question performance finalized:', this.currentQuestionPerformance);
  }

  /**
   * Hide all answer input types
   */
  hideAllAnswerTypes() {
    document.getElementById('multiple-choice-options').classList.add('hidden');
    document.getElementById('numeric-input').classList.add('hidden');
    document.getElementById('short-answer-input').classList.add('hidden');

    // Hide all interactive question types
    const interactiveContainers = [
      'number-line-slider',
      'drag-drop-matching',
      'visual-calculator',
      'number-bonds',
      'step-by-step-guided',
      'coordinate-plotting',
      'ratio-sliders',
      'equation-builders',
      'pattern-completion'
    ];

    interactiveContainers.forEach(containerId => {
      const container = document.getElementById(containerId);
      if (container) {
        container.classList.add('hidden');
      }
    });
  }

  /**
   * Show multiple choice options
   */
  showMultipleChoice(question) {
    const container = document.getElementById('multiple-choice-options');
    const buttons = container.querySelectorAll('.option-btn');

    // Validate that question has options
    if (!question.options || !Array.isArray(question.options) || question.options.length === 0) {
      console.error('Multiple choice question missing options:', question);
      // Fall back to short answer input
      this.showShortAnswerInput();
      return;
    }

    buttons.forEach((btn, index) => {
      if (index < question.options.length) {
        btn.textContent = question.options[index];
        btn.style.display = 'block';
        btn.classList.remove('selected');
      } else {
        btn.style.display = 'none';
      }
    });
    
    container.classList.remove('hidden');
  }

  /**
   * Show numeric input
   */
  showNumericInput() {
    document.getElementById('numeric-input').classList.remove('hidden');
    document.getElementById('numeric-answer').value = '';
    document.getElementById('numeric-answer').focus();
  }

  /**
   * Show short answer input
   */
  showShortAnswerInput() {
    document.getElementById('short-answer-input').classList.remove('hidden');
    document.getElementById('short-answer').value = '';
    document.getElementById('short-answer').focus();
  }

  /**
   * Show number line slider with HTML5 Canvas implementation
   */
  showNumberLineSlider(question) {
    const container = document.getElementById('number-line-slider');

    // Get configuration from question or use defaults
    const config = question.config || question.numberLineConfig || {};
    const { min = -10, max = 10, step = 1, snapToGrid = true } = config;

    // Store configuration for this question
    this.numberLineConfig = { min, max, step, snapToGrid };
    this.currentNumberLineValue = (min + max) / 2; // Start in middle

    // Set up the number line display
    this.setupNumberLineDisplay(min, max, step);

    // Initialize interaction handlers
    this.setupNumberLineInteraction();

    // Show the container
    container.classList.remove('hidden');

    console.log('Number line slider initialized:', { min, max, step, snapToGrid });
  }

  /**
   * Set up number line display elements
   */
  setupNumberLineDisplay(min, max, step) {
    const track = document.getElementById('number-line-track');
    const handle = document.getElementById('number-line-handle');
    const valueDisplay = document.getElementById('number-line-value');
    const labelsContainer = document.getElementById('number-line-labels');

    // Clear and create labels
    labelsContainer.innerHTML = '';
    const labelCount = Math.min(11, Math.abs(max - min) + 1);

    for (let i = 0; i < labelCount; i++) {
      const value = min + (i * (max - min) / (labelCount - 1));
      const label = document.createElement('span');
      label.textContent = step < 1 ? value.toFixed(1) : Math.round(value);
      labelsContainer.appendChild(label);
    }

    // Set initial handle position and value
    this.updateNumberLinePosition();
  }

  /**
   * Update number line handle position and value display
   */
  updateNumberLinePosition() {
    const handle = document.getElementById('number-line-handle');
    const valueDisplay = document.getElementById('number-line-value');
    const { min, max, step } = this.numberLineConfig;

    // Calculate percentage position
    const percentage = ((this.currentNumberLineValue - min) / (max - min)) * 100;
    handle.style.left = `${Math.max(0, Math.min(100, percentage))}%`;

    // Update value display
    const displayValue = step < 1 ?
      this.currentNumberLineValue.toFixed(1) :
      Math.round(this.currentNumberLineValue);
    valueDisplay.textContent = displayValue;

    // Enable next button when value is set
    document.getElementById('next-question-btn').disabled = false;
  }

  /**
   * Set up number line interaction handlers
   */
  setupNumberLineInteraction() {
    const track = document.getElementById('number-line-track');
    const handle = document.getElementById('number-line-handle');

    let isDragging = false;

    // Mouse events
    const handleMouseDown = (e) => {
      isDragging = true;
      e.preventDefault();
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      handle.style.cursor = 'grabbing';
    };

    const handleMouseMove = (e) => {
      if (!isDragging) return;
      this.updateNumberLineFromEvent(e);
    };

    const handleMouseUp = () => {
      isDragging = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      handle.style.cursor = 'grab';
    };

    // Touch events for mobile
    const handleTouchStart = (e) => {
      isDragging = true;
      e.preventDefault();
    };

    const handleTouchMove = (e) => {
      if (!isDragging) return;
      e.preventDefault();
      this.updateNumberLineFromEvent(e.touches[0]);
    };

    const handleTouchEnd = () => {
      isDragging = false;
    };

    // Track click to jump to position
    const handleTrackClick = (e) => {
      if (e.target === handle) return;
      this.updateNumberLineFromEvent(e);
    };

    // Keyboard support
    const handleKeyDown = (e) => {
      const { min, max, step } = this.numberLineConfig;
      let newValue = this.currentNumberLineValue;

      switch (e.key) {
        case 'ArrowLeft':
        case 'ArrowDown':
          newValue = Math.max(min, newValue - step);
          break;
        case 'ArrowRight':
        case 'ArrowUp':
          newValue = Math.min(max, newValue + step);
          break;
        case 'Home':
          newValue = min;
          break;
        case 'End':
          newValue = max;
          break;
        default:
          return;
      }

      e.preventDefault();
      this.currentNumberLineValue = newValue;
      this.updateNumberLinePosition();
    };

    // Add event listeners
    handle.addEventListener('mousedown', handleMouseDown);
    handle.addEventListener('touchstart', handleTouchStart);
    handle.addEventListener('touchmove', handleTouchMove);
    handle.addEventListener('touchend', handleTouchEnd);
    handle.addEventListener('keydown', handleKeyDown);
    track.addEventListener('click', handleTrackClick);

    // Make handle focusable for keyboard navigation
    handle.setAttribute('tabindex', '0');
    handle.setAttribute('role', 'slider');
    handle.setAttribute('aria-valuemin', this.numberLineConfig.min);
    handle.setAttribute('aria-valuemax', this.numberLineConfig.max);
    handle.setAttribute('aria-valuenow', this.currentNumberLineValue);

    // Store cleanup function
    this.numberLineCleanup = () => {
      handle.removeEventListener('mousedown', handleMouseDown);
      handle.removeEventListener('touchstart', handleTouchStart);
      handle.removeEventListener('touchmove', handleTouchMove);
      handle.removeEventListener('touchend', handleTouchEnd);
      handle.removeEventListener('keydown', handleKeyDown);
      track.removeEventListener('click', handleTrackClick);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }

  /**
   * Update number line value from mouse/touch event
   */
  updateNumberLineFromEvent(event) {
    const track = document.getElementById('number-line-track');
    const rect = track.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));

    const { min, max, step, snapToGrid } = this.numberLineConfig;
    let value = min + (percentage / 100) * (max - min);

    if (snapToGrid) {
      value = Math.round(value / step) * step;
      value = Math.max(min, Math.min(max, value));
    }

    this.currentNumberLineValue = value;
    this.updateNumberLinePosition();

    // Update ARIA attributes
    const handle = document.getElementById('number-line-handle');
    handle.setAttribute('aria-valuenow', value);
  }

  // REMOVED: Old number line implementation methods

  // REMOVED: Old number line event handling methods

  // REMOVED: Old percentage number line implementation

  /**
   * Show drag and drop matching using interact.js
   */
  showDragDropMatching(question) {
    const container = document.getElementById('drag-drop-matching');
    const draggableItemsContainer = document.getElementById('draggable-items');
    const dropZonesContainer = document.getElementById('drop-zones');

    // Get configuration from question
    const config = question.config || question.dragDropConfig || {};
    const { items = [], zones = [], correctMatches = {} } = config;

    // Clear containers
    draggableItemsContainer.innerHTML = '';
    dropZonesContainer.innerHTML = '';

    // Initialize drag and drop state
    this.dragDropState = {
      matches: new Map(),
      items: items,
      zones: zones,
      correctMatches: correctMatches
    };

    // Create draggable items
    items.forEach((item, index) => {
      const draggableItem = document.createElement('div');
      draggableItem.className = 'draggable-item';
      draggableItem.textContent = item.text;
      draggableItem.dataset.itemId = item.id || index;
      draggableItem.dataset.originalText = item.text;
      draggableItemsContainer.appendChild(draggableItem);
    });

    // Create drop zones
    zones.forEach((zone, index) => {
      const dropZone = document.createElement('div');
      dropZone.className = 'drop-zone';
      dropZone.dataset.zoneId = zone.id || index;
      dropZone.innerHTML = `
        <div class="drop-zone-label">${zone.label}</div>
        <div class="drop-zone-content">Drop here</div>
      `;
      dropZonesContainer.appendChild(dropZone);
    });

    // Set up interact.js drag and drop
    this.setupInteractDragDrop();

    // Set up reset button
    this.setupDragDropResetButton();

    // Show the container
    container.classList.remove('hidden');

    console.log('Drag and drop matching initialized:', { items: items.length, zones: zones.length });
  }

  /**
   * Set up interact.js drag and drop functionality
   */
  setupInteractDragDrop() {
    // Check if interact.js is available
    if (typeof interact === 'undefined') {
      console.error('Interact.js library not loaded');
      return;
    }

    // Verify draggable items exist
    const draggableItems = document.querySelectorAll('.draggable-item');
    const dropZones = document.querySelectorAll('.drop-zone');

    console.log('Setting up drag-drop:', {
      draggableItems: draggableItems.length,
      dropZones: dropZones.length
    });

    if (draggableItems.length === 0 || dropZones.length === 0) {
      console.error('Drag-drop elements not found');
      return;
    }

    // Make items draggable
    interact('.draggable-item')
      .draggable({
        inertia: true,
        modifiers: [
          interact.modifiers.restrictRect({
            restriction: '.matching-container',
            endOnly: true
          })
        ],
        autoScroll: true,
        listeners: {
          start: (event) => {
            event.target.classList.add('dragging');
            event.target.style.zIndex = '1000';
            console.log('Drag started:', event.target.textContent);
          },
          move: (event) => {
            const target = event.target;
            const x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
            const y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

            target.style.transform = `translate(${x}px, ${y}px)`;
            target.setAttribute('data-x', x);
            target.setAttribute('data-y', y);
          },
          end: (event) => {
            event.target.classList.remove('dragging');
            event.target.style.zIndex = '';
            console.log('Drag ended:', event.target.textContent);
          }
        }
      });

    // Make zones droppable
    interact('.drop-zone')
      .dropzone({
        accept: '.draggable-item',
        overlap: 0.5, // Reduced overlap requirement
        listeners: {
          dragenter: (event) => {
            const dropZone = event.target.closest('.drop-zone');
            if (dropZone) {
              dropZone.classList.add('drag-over');
              console.log('Drag enter zone:', dropZone.dataset.zoneId);
            }
          },
          dragleave: (event) => {
            const dropZone = event.target.closest('.drop-zone');
            if (dropZone) {
              dropZone.classList.remove('drag-over');
            }
          },
          drop: (event) => {
            const draggableElement = event.relatedTarget;
            const dropzoneElement = event.target.closest('.drop-zone') || event.target;

            console.log('Drop event triggered:', {
              draggable: draggableElement?.textContent,
              zone: dropzoneElement?.dataset?.zoneId
            });

            if (draggableElement && dropzoneElement) {
              this.handleInteractDrop(draggableElement, dropzoneElement);
            } else {
              console.error('Drop failed: missing elements');
            }
          }
        }
      });

    // Add fallback click-to-match functionality
    this.setupClickToMatchFallback();

    // Store cleanup function
    this.dragDropCleanup = () => {
      if (typeof interact !== 'undefined') {
        interact('.draggable-item').unset();
        interact('.drop-zone').unset();
      }
      this.cleanupClickToMatch();
    };

    console.log('Drag-drop setup completed');
  }

  /**
   * Set up click-to-match fallback for drag-and-drop
   */
  setupClickToMatchFallback() {
    let selectedItem = null;

    // Handle item selection
    const handleItemClick = (e) => {
      const item = e.target.closest('.draggable-item');
      if (!item) return;

      // Clear previous selection
      document.querySelectorAll('.draggable-item').forEach(el => {
        el.classList.remove('selected');
      });

      // Select this item
      selectedItem = item;
      item.classList.add('selected');

      // Show instruction
      this.showDragDropInstruction('Now click on a drop zone to match this item');

      console.log('Item selected for matching:', item.textContent);
    };

    // Handle zone selection
    const handleZoneClick = (e) => {
      const zone = e.target.closest('.drop-zone');
      if (!zone || !selectedItem) return;

      // Perform the match
      this.handleInteractDrop(selectedItem, zone);

      // Clear selection
      selectedItem.classList.remove('selected');
      selectedItem = null;

      this.hideDragDropInstruction();
    };

    // Add event listeners
    document.querySelectorAll('.draggable-item').forEach(item => {
      item.addEventListener('click', handleItemClick);
      item.style.cursor = 'pointer';
    });

    document.querySelectorAll('.drop-zone').forEach(zone => {
      zone.addEventListener('click', handleZoneClick);
      zone.style.cursor = 'pointer';
    });

    // Store cleanup function
    this.clickToMatchCleanup = () => {
      document.querySelectorAll('.draggable-item').forEach(item => {
        item.removeEventListener('click', handleItemClick);
      });
      document.querySelectorAll('.drop-zone').forEach(zone => {
        zone.removeEventListener('click', handleZoneClick);
      });
    };
  }

  /**
   * Clean up click-to-match functionality
   */
  cleanupClickToMatch() {
    if (this.clickToMatchCleanup) {
      this.clickToMatchCleanup();
      this.clickToMatchCleanup = null;
    }
  }

  /**
   * Show drag-drop instruction
   */
  showDragDropInstruction(message) {
    let instructionEl = document.getElementById('drag-drop-instruction');
    if (!instructionEl) {
      instructionEl = document.createElement('div');
      instructionEl.id = 'drag-drop-instruction';
      instructionEl.className = 'drag-drop-instruction';
      instructionEl.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #1547bb;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        z-index: 10000;
        font-weight: bold;
      `;
      document.body.appendChild(instructionEl);
    }
    instructionEl.textContent = message;
    instructionEl.style.display = 'block';
  }

  /**
   * Hide drag-drop instruction
   */
  hideDragDropInstruction() {
    const instructionEl = document.getElementById('drag-drop-instruction');
    if (instructionEl) {
      instructionEl.style.display = 'none';
    }
  }

  /**
   * Handle item drop using interact.js
   */
  handleInteractDrop(draggableElement, dropzoneElement) {
    const itemId = draggableElement.dataset.itemId;
    const zoneId = dropzoneElement.dataset.zoneId;

    // Log interaction for performance tracking
    this.logInteraction('drag_drop', {
      action: 'drop',
      itemId: itemId,
      zoneId: zoneId,
      itemText: draggableElement.textContent,
      zoneLabel: dropzoneElement.dataset.zoneLabel || dropzoneElement.textContent
    });

    console.log('Item dropped:', { itemId, zoneId });

    // Remove drag-over styling
    dropzoneElement.classList.remove('drag-over');

    // Check if zone already has an item
    const existingItemId = this.dragDropState.matches.get(zoneId);
    if (existingItemId) {
      // Return existing item to original position
      const existingItem = document.querySelector(`[data-item-id="${existingItemId}"]`);
      if (existingItem) {
        this.resetItemPosition(existingItem);
      }
    }

    // Check if item was in another zone
    this.dragDropState.matches.forEach((value, key) => {
      if (value === itemId && key !== zoneId) {
        this.dragDropState.matches.delete(key);
        const prevZone = document.querySelector(`[data-zone-id="${key}"]`);
        if (prevZone) {
          prevZone.classList.remove('filled');
          prevZone.querySelector('.drop-zone-content').textContent = 'Drop here';
        }
      }
    });

    // Place item in new zone
    this.dragDropState.matches.set(zoneId, itemId);
    dropzoneElement.classList.add('filled');
    dropzoneElement.querySelector('.drop-zone-content').textContent = draggableElement.textContent;

    // Position item in the zone
    this.positionItemInZone(draggableElement, dropzoneElement);

    // Log solution step
    this.logInteraction('solution_step', {
      step: `Matched "${draggableElement.textContent}" to zone ${zoneId}`,
      progress: this.dragDropState.matches.size / this.dragDropState.zones.length
    });

    // Check completion
    this.checkDragDropCompletion();
  }

  /**
   * Position draggable item within the drop zone
   */
  positionItemInZone(item, zone) {
    const zoneRect = zone.getBoundingClientRect();
    const containerRect = zone.closest('.matching-container').getBoundingClientRect();

    const x = zoneRect.left - containerRect.left + (zoneRect.width - item.offsetWidth) / 2;
    const y = zoneRect.top - containerRect.top + (zoneRect.height - item.offsetHeight) / 2;

    item.style.transform = `translate(${x}px, ${y}px)`;
    item.setAttribute('data-x', x);
    item.setAttribute('data-y', y);
  }

  /**
   * Reset item to original position
   */
  resetItemPosition(item) {
    item.style.transform = 'translate(0px, 0px)';
    item.setAttribute('data-x', 0);
    item.setAttribute('data-y', 0);
  }

  /**
   * Check if drag and drop is complete
   */
  checkDragDropCompletion() {
    const totalZones = this.dragDropState.zones.length;
    const filledZones = this.dragDropState.matches.size;

    // Enable next button if at least one match is made
    const hasMatches = filledZones > 0;
    const isComplete = filledZones === totalZones;
    document.getElementById('next-question-btn').disabled = !hasMatches;

    if (hasMatches) {
      this.showCompletionIndicator();
      this.clearFallbackTimer();

      // Finalize performance tracking if complete
      if (isComplete) {
        this.finalizeQuestionPerformance(true);
      }
    }

    console.log('Drag drop completion check:', { filledZones, totalZones, hasMatches, isComplete });
  }

  /**
   * Get drag and drop answer
   */
  getDragDropAnswer() {
    if (!this.dragDropState) {
      return JSON.stringify({
        type: 'drag-drop',
        matches: {},
        completed: false,
        error: 'No drag-drop state found'
      });
    }

    const matches = {};
    const matchDetails = [];

    this.dragDropState.matches.forEach((itemId, zoneId) => {
      const zone = this.dragDropState.zones.find(z => (z.id || this.dragDropState.zones.indexOf(z)) == zoneId);
      const item = this.dragDropState.items.find(i => (i.id || this.dragDropState.items.indexOf(i)) == itemId);

      if (zone && item) {
        matches[zone.label] = item.text;
        matchDetails.push({
          item: item.text,
          zone: zone.label,
          itemId: itemId,
          zoneId: zoneId
        });
      }
    });

    // Get performance data for this question
    const performanceData = this.currentQuestionPerformance || {};
    const questionIndex = this.currentQuestionIndex;
    const interactionLogs = this.interactionLogs[questionIndex] || [];

    return JSON.stringify({
      type: 'drag-drop',
      matches: matches,
      matchDetails: matchDetails,
      totalMatches: this.dragDropState.matches.size,
      totalZones: this.dragDropState.zones.length,
      completed: this.dragDropState.matches.size > 0,
      timestamp: new Date().toISOString(),

      // Enhanced performance data
      performanceMetrics: {
        timeSpent: performanceData.completionTime ?
          performanceData.completionTime - performanceData.startTime : null,
        firstInteractionTime: performanceData.firstInteractionTime ?
          performanceData.firstInteractionTime - performanceData.startTime : null,
        totalInteractions: performanceData.difficultyIndicators?.totalInteractions || 0,
        attempts: performanceData.attempts || 0,
        hintsUsed: performanceData.hintsUsed || 0,
        errorsCount: performanceData.errorsCount || 0,
        completionEfficiency: performanceData.difficultyIndicators?.completionEfficiency || 0,
        errorRate: performanceData.difficultyIndicators?.errorRate || 0,
        hintDependency: performanceData.difficultyIndicators?.hintDependency || false
      },

      // Detailed interaction logs
      interactionSequence: interactionLogs.map(log => ({
        type: log.type,
        timestamp: log.timestamp,
        timeFromStart: log.timeFromStart,
        details: log.details
      })),

      // Solution path analysis
      solutionPath: performanceData.solutionPath || [],

      // Engagement metrics
      engagementMetrics: {
        timeToFirstInteraction: performanceData.difficultyIndicators?.timeToFirstInteraction || null,
        interactionDensity: performanceData.difficultyIndicators?.totalInteractions > 0 ?
          (performanceData.completionTime - performanceData.startTime) / performanceData.difficultyIndicators.totalInteractions : 0,
        persistenceScore: Math.max(0, 1 - (performanceData.errorsCount || 0) / Math.max(1, performanceData.attempts || 1))
      }
    });
  }

  /**
   * Set up reset button for drag and drop
   */
  setupDragDropResetButton() {
    const resetBtn = document.getElementById('reset-drag-drop-btn');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        this.resetDragDropQuestion();
      });
    }
  }

  /**
   * Reset drag and drop question to initial state
   */
  resetDragDropQuestion() {
    if (!this.dragDropState) return;

    // Clear all matches
    this.dragDropState.matches.clear();

    // Reset all drop zones
    const dropZones = document.querySelectorAll('.drop-zone');
    dropZones.forEach(zone => {
      zone.classList.remove('filled', 'drag-over');
      const contentElement = zone.querySelector('.drop-zone-content');
      if (contentElement) {
        contentElement.textContent = 'Drop here';
      }
    });

    // Return all items to original positions
    const allItems = document.querySelectorAll('.draggable-item');
    allItems.forEach(item => {
      this.resetItemPosition(item);
      item.classList.remove('dragging');
    });

    // Disable next button
    document.getElementById('next-question-btn').disabled = true;

    console.log('Drag and drop question reset to initial state');
  }

  // REMOVED: Old drag and drop event handling

  // REMOVED: Old drag and drop handling methods

  // REMOVED: Old touch event handlers and reset functionality

  /**
   * Show visual calculator interactive question
   */
  showVisualCalculator(question) {
    const container = document.getElementById('visual-calculator');
    const keypadContainer = document.getElementById('calculator-keypad');
    const displayElement = document.getElementById('current-display');
    const stepsElement = document.getElementById('calculation-steps');

    // Get configuration from question
    const config = question.config || {};
    const {
      calculationType = 'basic',
      targetCalculation = '',
      expectedResult = 0,
      allowedOperations = ['+', '-', '×', '÷'],
      showSteps = true,
      hint = ''
    } = config;

    // Initialize visual calculator state
    this.visualCalculatorState = {
      currentDisplay: '0',
      calculationSteps: [],
      currentInput: '',
      operator: null,
      previousValue: null,
      isNewInput: true,
      targetCalculation: targetCalculation,
      expectedResult: expectedResult,
      allowedOperations: allowedOperations,
      showSteps: showSteps,
      hint: hint,
      isComplete: false
    };

    // Clear display
    displayElement.textContent = '0';
    stepsElement.innerHTML = showSteps ? `<div class="target-calculation">Calculate: ${targetCalculation}</div>` : '';

    // Create calculator keypad
    this.createCalculatorKeypad();

    // Set up controls
    this.setupVisualCalculatorControls();

    // Show the container
    container.classList.remove('hidden');

    console.log('Visual calculator initialized:', { targetCalculation, expectedResult });
  }

  // REMOVED: Area model drawing methods

  // REMOVED: Rectangle area model drawing

  // REMOVED: Circle area model and interaction setup

  // REMOVED: Area model click handling

  /**
   * Create calculator keypad buttons
   */
  createCalculatorKeypad() {
    const keypadContainer = document.getElementById('calculator-keypad');
    keypadContainer.innerHTML = '';

    // Define calculator layout (Casio-style)
    const buttons = [
      ['C', '±', '%', '÷'],
      ['7', '8', '9', '×'],
      ['4', '5', '6', '-'],
      ['1', '2', '3', '+'],
      ['0', '.', '=', '=']
    ];

    buttons.forEach(row => {
      row.forEach((buttonText, index) => {
        // Skip duplicate equals button
        if (buttonText === '=' && index === 3) return;

        const button = document.createElement('button');
        button.className = 'calc-btn';
        button.textContent = buttonText;
        button.dataset.value = buttonText;

        // Add special classes for styling
        if (['+', '-', '×', '÷'].includes(buttonText)) {
          button.classList.add('operator');
        } else if (buttonText === '=') {
          button.classList.add('equals');
          button.style.gridColumn = 'span 2'; // Make equals button wider
        } else if (buttonText === 'C') {
          button.classList.add('clear');
        } else if (buttonText === '0') {
          button.style.gridColumn = 'span 2'; // Make 0 button wider
        }

        // Add click handler
        button.addEventListener('click', () => {
          this.handleCalculatorButtonClick(buttonText);
        });

        keypadContainer.appendChild(button);
      });
    });
  }

  /**
   * Handle calculator button clicks
   */
  handleCalculatorButtonClick(buttonValue) {
    const { allowedOperations } = this.visualCalculatorState;

    // Log interaction for performance tracking
    this.logInteraction('calculator_button', {
      button: buttonValue,
      currentDisplay: this.visualCalculatorState.currentDisplay,
      calculationSteps: this.visualCalculatorState.calculationSteps.length
    });

    // Check if operation is allowed
    if (['+', '-', '×', '÷'].includes(buttonValue) && !allowedOperations.includes(buttonValue)) {
      console.log('Operation not allowed:', buttonValue);
      this.logInteraction('error', {
        type: 'operation_not_allowed',
        button: buttonValue
      });
      return;
    }

    switch (buttonValue) {
      case 'C':
        this.clearCalculator();
        break;
      case '=':
        this.calculateResult();
        break;
      case '+':
      case '-':
      case '×':
      case '÷':
        this.handleOperator(buttonValue);
        break;
      case '.':
        this.handleDecimal();
        break;
      case '±':
        this.toggleSign();
        break;
      case '%':
        this.handlePercent();
        break;
      default:
        if (/^\d$/.test(buttonValue)) {
          this.handleNumber(buttonValue);
        }
    }

    this.updateDisplay();
    this.checkCalculatorCompletion();
  }

  /**
   * Handle number input
   */
  handleNumber(number) {
    const state = this.visualCalculatorState;

    if (state.isNewInput) {
      state.currentInput = number;
      state.isNewInput = false;
    } else {
      state.currentInput = state.currentInput === '0' ? number : state.currentInput + number;
    }

    state.currentDisplay = state.currentInput;
  }

  /**
   * Handle operator input
   */
  handleOperator(operator) {
    const state = this.visualCalculatorState;

    if (state.previousValue !== null && !state.isNewInput) {
      this.calculateResult();
    }

    state.operator = operator;
    state.previousValue = parseFloat(state.currentInput || state.currentDisplay);
    state.isNewInput = true;

    // Add step to calculation history
    if (state.showSteps) {
      this.addCalculationStep(`${state.previousValue} ${operator}`);
    }
  }

  /**
   * Calculate result
   */
  calculateResult() {
    const state = this.visualCalculatorState;

    if (state.operator && state.previousValue !== null) {
      const current = parseFloat(state.currentInput || state.currentDisplay);
      let result;

      switch (state.operator) {
        case '+':
          result = state.previousValue + current;
          break;
        case '-':
          result = state.previousValue - current;
          break;
        case '×':
          result = state.previousValue * current;
          break;
        case '÷':
          result = current !== 0 ? state.previousValue / current : 'Error';
          break;
        default:
          result = current;
      }

      if (result !== 'Error') {
        state.currentDisplay = result.toString();
        state.currentInput = result.toString();

        // Add final step to calculation history
        if (state.showSteps) {
          this.addCalculationStep(`${state.previousValue} ${state.operator} ${current} = ${result}`);
        }

        // Check if this matches the target calculation
        this.checkTargetCalculation(result);
      } else {
        state.currentDisplay = 'Error';
      }

      state.operator = null;
      state.previousValue = null;
      state.isNewInput = true;
    }
  }

  /**
   * Clear calculator
   */
  clearCalculator() {
    const state = this.visualCalculatorState;

    state.currentDisplay = '0';
    state.currentInput = '';
    state.operator = null;
    state.previousValue = null;
    state.isNewInput = true;
    state.calculationSteps = [];

    // Reset steps display
    const stepsElement = document.getElementById('calculation-steps');
    stepsElement.innerHTML = state.showSteps ? `<div class="target-calculation">Calculate: ${state.targetCalculation}</div>` : '';
  }

  /**
   * Handle decimal point
   */
  handleDecimal() {
    const state = this.visualCalculatorState;

    if (state.isNewInput) {
      state.currentInput = '0.';
      state.isNewInput = false;
    } else if (!state.currentInput.includes('.')) {
      state.currentInput += '.';
    }

    state.currentDisplay = state.currentInput;
  }

  /**
   * Toggle sign (+/-)
   */
  toggleSign() {
    const state = this.visualCalculatorState;
    const current = parseFloat(state.currentDisplay);

    if (current !== 0) {
      const newValue = (-current).toString();
      state.currentDisplay = newValue;
      state.currentInput = newValue;
    }
  }

  /**
   * Handle percent
   */
  handlePercent() {
    const state = this.visualCalculatorState;
    const current = parseFloat(state.currentDisplay);
    const result = (current / 100).toString();

    state.currentDisplay = result;
    state.currentInput = result;
  }

  /**
   * Update calculator display
   */
  updateDisplay() {
    const displayElement = document.getElementById('current-display');
    displayElement.textContent = this.visualCalculatorState.currentDisplay;
  }

  /**
   * Add calculation step to history
   */
  addCalculationStep(step) {
    const stepsElement = document.getElementById('calculation-steps');
    const stepDiv = document.createElement('div');
    stepDiv.className = 'calculation-step';
    stepDiv.textContent = step;
    stepsElement.appendChild(stepDiv);

    this.visualCalculatorState.calculationSteps.push(step);
  }

  /**
   * Check if target calculation is achieved
   */
  checkTargetCalculation(result) {
    const state = this.visualCalculatorState;

    if (Math.abs(result - state.expectedResult) < 0.001) {
      state.isComplete = true;

      // Show success message
      const stepsElement = document.getElementById('calculation-steps');
      const successDiv = document.createElement('div');
      successDiv.className = 'calculation-success';
      successDiv.style.cssText = `
        color: #28a745;
        font-weight: bold;
        margin-top: 10px;
        text-align: center;
      `;
      successDiv.textContent = '✓ Correct! Well done.';
      stepsElement.appendChild(successDiv);

      console.log('Target calculation achieved:', { result, expected: state.expectedResult });
    }
  }

  /**
   * Set up visual calculator controls
   */
  setupVisualCalculatorControls() {
    const clearBtn = document.getElementById('clear-calculator-btn');
    const stepBtn = document.getElementById('step-calculator-btn');

    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearCalculator();
      });
    }

    if (stepBtn) {
      stepBtn.addEventListener('click', () => {
        this.showCalculatorHint();
      });
    }

    // Store cleanup function
    this.visualCalculatorCleanup = () => {
      if (clearBtn) clearBtn.removeEventListener('click', this.clearCalculator);
      if (stepBtn) stepBtn.removeEventListener('click', this.showCalculatorHint);

      // Remove button click handlers
      document.querySelectorAll('.calc-btn').forEach(button => {
        button.removeEventListener('click', this.handleCalculatorButtonClick);
      });
    };
  }

  /**
   * Show calculator hint
   */
  showCalculatorHint() {
    // Log hint request for performance tracking
    this.logInteraction('hint_request', {
      questionType: 'visual-calculator',
      currentStep: this.visualCalculatorState.calculationSteps.length
    });

    const { hint } = this.visualCalculatorState;
    const hintText = hint || 'Follow the calculation step by step';

    // Create or update hint display
    let hintDisplay = document.getElementById('calculator-hint-display');
    if (!hintDisplay) {
      hintDisplay = document.createElement('div');
      hintDisplay.id = 'calculator-hint-display';
      hintDisplay.className = 'calculator-hint-display';
      hintDisplay.style.cssText = `
        background: #fff3cd;
        color: #856404;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #ffeaa7;
        margin-top: 15px;
        font-style: italic;
        text-align: center;
      `;

      const container = document.getElementById('visual-calculator');
      container.appendChild(hintDisplay);
    }

    hintDisplay.textContent = `💡 Hint: ${hintText}`;
    hintDisplay.style.display = 'block';

    console.log('Calculator hint shown:', hintText);
  }

  /**
   * Show number bonds interactive question
   */
  showNumberBonds(question) {
    const container = document.getElementById('number-bonds');

    // Get configuration from question
    const config = question.config || {};
    const {
      bondType = 'addition',
      targetNumber = 10,
      knownNumber = null,
      missingNumber = null,
      startNumber = null,
      possiblePairs = [],
      correctPairs = [],
      visualStyle = 'circles',
      allowMultiple = false,
      showHints = true,
      hint = ''
    } = config;

    // Initialize number bonds state
    this.numberBondsState = {
      bondType: bondType,
      targetNumber: targetNumber,
      knownNumber: knownNumber,
      missingNumber: missingNumber,
      startNumber: startNumber,
      possiblePairs: possiblePairs,
      correctPairs: correctPairs,
      visualStyle: visualStyle,
      allowMultiple: allowMultiple,
      showHints: showHints,
      hint: hint,
      userAnswers: [],
      isComplete: false
    };

    // Create the number bonds interface
    this.createNumberBondsInterface();

    // Set up controls
    this.setupNumberBondsControls();

    // Show the container
    container.classList.remove('hidden');

    console.log('Number bonds initialized:', { bondType, targetNumber, visualStyle });
  }

  /**
   * Create number bonds interface
   */
  createNumberBondsInterface() {
    const container = document.getElementById('number-bonds');
    const { bondType, targetNumber, knownNumber, visualStyle } = this.numberBondsState;

    // Clear container
    container.innerHTML = '';

    // Create main structure
    const bondsContainer = document.createElement('div');
    bondsContainer.className = 'number-bonds-container';

    // Create visual representation
    const visualContainer = document.createElement('div');
    visualContainer.className = 'bonds-visual-container';
    visualContainer.id = 'bonds-visual-container';

    // Create input area
    const inputContainer = document.createElement('div');
    inputContainer.className = 'bonds-input-container';
    inputContainer.id = 'bonds-input-container';

    // Create controls
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'bonds-controls-container';
    controlsContainer.innerHTML = `
      <button id="clear-bonds-btn" class="reset-btn">
        <span class="btn-icon">↺</span>
        <span class="btn-text">Clear</span>
      </button>
      <button id="hint-bonds-btn" class="hint-btn">
        <span class="btn-icon">💡</span>
        <span class="btn-text">Hint</span>
      </button>
    `;

    // Add hint text
    const hintContainer = document.createElement('p');
    hintContainer.className = 'input-hint';
    hintContainer.textContent = this.getNumberBondsHintText();

    // Assemble interface
    bondsContainer.appendChild(visualContainer);
    bondsContainer.appendChild(inputContainer);
    bondsContainer.appendChild(controlsContainer);
    container.appendChild(bondsContainer);
    container.appendChild(hintContainer);

    // Generate specific interface based on bond type
    this.generateBondTypeInterface();
  }

  /**
   * Generate interface based on bond type
   */
  generateBondTypeInterface() {
    const { bondType } = this.numberBondsState;

    switch (bondType) {
      case 'addition':
        this.createAdditionBondInterface();
        break;
      case 'subtraction':
        this.createSubtractionBondInterface();
        break;
      case 'decomposition':
        this.createDecompositionBondInterface();
        break;
      default:
        this.createAdditionBondInterface();
    }
  }

  /**
   * Create addition bond interface (e.g., 8 + ? = 15)
   */
  createAdditionBondInterface() {
    const visualContainer = document.getElementById('bonds-visual-container');
    const inputContainer = document.getElementById('bonds-input-container');
    const { targetNumber, knownNumber, visualStyle } = this.numberBondsState;

    // Visual representation
    visualContainer.innerHTML = `
      <div class="bond-equation">
        <div class="bond-visual ${visualStyle}">
          ${this.createVisualRepresentation(knownNumber, visualStyle)}
        </div>
        <div class="bond-operator">+</div>
        <div class="bond-visual missing">
          <div class="missing-placeholder">?</div>
        </div>
        <div class="bond-equals">=</div>
        <div class="bond-result">
          ${this.createVisualRepresentation(targetNumber, visualStyle)}
        </div>
      </div>
    `;

    // Input area
    inputContainer.innerHTML = `
      <div class="bond-input-area">
        <span class="bond-prompt">${knownNumber} + </span>
        <input type="number" id="missing-number-input" class="bond-input" min="0" max="20" placeholder="?">
        <span class="bond-prompt"> = ${targetNumber}</span>
        <button id="check-bond-btn" class="check-btn">Check</button>
      </div>
    `;
  }

  /**
   * Create subtraction bond interface (e.g., 20 - ? = 13)
   */
  createSubtractionBondInterface() {
    const visualContainer = document.getElementById('bonds-visual-container');
    const inputContainer = document.getElementById('bonds-input-container');
    const { targetNumber, startNumber, visualStyle } = this.numberBondsState;

    // Visual representation
    visualContainer.innerHTML = `
      <div class="bond-equation">
        <div class="bond-visual ${visualStyle}">
          ${this.createVisualRepresentation(startNumber, visualStyle)}
        </div>
        <div class="bond-operator">-</div>
        <div class="bond-visual missing">
          <div class="missing-placeholder">?</div>
        </div>
        <div class="bond-equals">=</div>
        <div class="bond-result">
          ${this.createVisualRepresentation(targetNumber, visualStyle)}
        </div>
      </div>
    `;

    // Input area
    inputContainer.innerHTML = `
      <div class="bond-input-area">
        <span class="bond-prompt">${startNumber} - </span>
        <input type="number" id="missing-number-input" class="bond-input" min="0" max="20" placeholder="?">
        <span class="bond-prompt"> = ${targetNumber}</span>
        <button id="check-bond-btn" class="check-btn">Check</button>
      </div>
    `;
  }

  /**
   * Create decomposition bond interface (e.g., 12 = ? + ?)
   */
  createDecompositionBondInterface() {
    const visualContainer = document.getElementById('bonds-visual-container');
    const inputContainer = document.getElementById('bonds-input-container');
    const { targetNumber, possiblePairs, visualStyle } = this.numberBondsState;

    // Visual representation
    visualContainer.innerHTML = `
      <div class="bond-equation">
        <div class="bond-result">
          ${this.createVisualRepresentation(targetNumber, visualStyle)}
        </div>
        <div class="bond-equals">=</div>
        <div class="bond-visual missing">
          <div class="missing-placeholder">?</div>
        </div>
        <div class="bond-operator">+</div>
        <div class="bond-visual missing">
          <div class="missing-placeholder">?</div>
        </div>
      </div>
    `;

    // Input area with multiple possible answers
    inputContainer.innerHTML = `
      <div class="bond-input-area">
        <span class="bond-prompt">${targetNumber} = </span>
        <input type="number" id="first-number-input" class="bond-input" min="0" max="20" placeholder="?">
        <span class="bond-prompt"> + </span>
        <input type="number" id="second-number-input" class="bond-input" min="0" max="20" placeholder="?">
        <button id="check-bond-btn" class="check-btn">Check</button>
      </div>
      <div class="found-pairs" id="found-pairs">
        <h5>Found pairs:</h5>
        <div class="pairs-list" id="pairs-list"></div>
      </div>
    `;
  }

  /**
   * Check calculator completion
   */
  checkCalculatorCompletion() {
    const hasInteraction = this.visualCalculatorState.calculationSteps.length > 0;
    const isComplete = this.visualCalculatorState.isComplete;

    // Enable next button if there's any interaction or completion
    document.getElementById('next-question-btn').disabled = !hasInteraction;

    if (isComplete) {
      this.showCompletionIndicator();
      this.clearFallbackTimer();

      // Finalize performance tracking
      this.finalizeQuestionPerformance(true);
    }

    console.log('Calculator completion check:', { hasInteraction, isComplete });
  }

  /**
   * Get visual calculator answer
   */
  getVisualCalculatorAnswer() {
    if (!this.visualCalculatorState) {
      return JSON.stringify({
        type: 'visual-calculator',
        calculationSteps: [],
        finalResult: null,
        isComplete: false,
        error: 'No visual calculator state found'
      });
    }

    const {
      calculationSteps,
      currentDisplay,
      targetCalculation,
      expectedResult,
      isComplete
    } = this.visualCalculatorState;

    // Get performance data for this question
    const performanceData = this.currentQuestionPerformance || {};
    const questionIndex = this.currentQuestionIndex;
    const interactionLogs = this.interactionLogs[questionIndex] || [];

    return JSON.stringify({
      type: 'visual-calculator',
      calculationSteps: calculationSteps,
      finalResult: currentDisplay,
      targetCalculation: targetCalculation,
      expectedResult: expectedResult,
      isCorrect: isComplete,
      isComplete: isComplete,
      timestamp: new Date().toISOString(),

      // Enhanced performance data
      performanceMetrics: {
        timeSpent: performanceData.completionTime ?
          performanceData.completionTime - performanceData.startTime : null,
        firstInteractionTime: performanceData.firstInteractionTime ?
          performanceData.firstInteractionTime - performanceData.startTime : null,
        totalInteractions: performanceData.difficultyIndicators?.totalInteractions || 0,
        buttonPresses: interactionLogs.filter(log => log.type === 'calculator_button').length,
        attempts: performanceData.attempts || 0,
        hintsUsed: performanceData.hintsUsed || 0,
        errorsCount: performanceData.errorsCount || 0,
        completionEfficiency: performanceData.difficultyIndicators?.completionEfficiency || 0,
        calculationAccuracy: isComplete ? 1 : 0
      },

      // Calculator-specific metrics
      calculatorMetrics: {
        stepsToCompletion: calculationSteps.length,
        operationsUsed: interactionLogs
          .filter(log => log.type === 'calculator_button' && ['+', '-', '×', '÷'].includes(log.details?.button))
          .map(log => log.details.button),
        numbersEntered: interactionLogs
          .filter(log => log.type === 'calculator_button' && /^\d$/.test(log.details?.button))
          .map(log => log.details.button),
        clearOperations: interactionLogs.filter(log => log.type === 'calculator_button' && log.details?.button === 'C').length
      },

      // Detailed interaction logs
      interactionSequence: interactionLogs.map(log => ({
        type: log.type,
        timestamp: log.timestamp,
        timeFromStart: log.timeFromStart,
        details: log.details
      })),

      // Solution path analysis
      solutionPath: performanceData.solutionPath || [],

      // Problem-solving approach analysis
      problemSolvingMetrics: {
        systematicApproach: calculationSteps.length > 0 && calculationSteps.every(step => step.includes('=')),
        errorRecovery: interactionLogs.filter(log => log.type === 'error').length > 0 ?
          interactionLogs.filter(log => log.type === 'calculator_button' && log.details?.button === 'C').length : 0,
        persistenceScore: Math.max(0, 1 - (performanceData.errorsCount || 0) / Math.max(1, performanceData.attempts || 1))
      }
    });
  }

  /**
   * Create visual representation for number bonds
   */
  createVisualRepresentation(number, style) {
    if (!number || number <= 0) return '';

    switch (style) {
      case 'circles':
        return this.createCircleRepresentation(number);
      case 'blocks':
        return this.createBlockRepresentation(number);
      case 'number-line':
        return this.createNumberLineRepresentation(number);
      default:
        return `<span class="number-display">${number}</span>`;
    }
  }

  /**
   * Create circle representation
   */
  createCircleRepresentation(number) {
    const circles = [];
    const maxCircles = Math.min(number, 20); // Limit visual complexity

    for (let i = 0; i < maxCircles; i++) {
      circles.push('<div class="visual-circle"></div>');
    }

    if (number > 20) {
      circles.push(`<span class="number-display">+${number - 20} more</span>`);
    }

    return `<div class="circles-container">${circles.join('')}</div>`;
  }

  /**
   * Create block representation
   */
  createBlockRepresentation(number) {
    const blocks = [];
    const maxBlocks = Math.min(number, 15); // Limit visual complexity

    for (let i = 0; i < maxBlocks; i++) {
      blocks.push('<div class="visual-block"></div>');
    }

    if (number > 15) {
      blocks.push(`<span class="number-display">+${number - 15} more</span>`);
    }

    return `<div class="blocks-container">${blocks.join('')}</div>`;
  }

  /**
   * Create number line representation
   */
  createNumberLineRepresentation(number) {
    return `<div class="number-line-display">
      <div class="number-line-value">${number}</div>
      <div class="number-line-bar" style="width: ${Math.min(number * 10, 200)}px;"></div>
    </div>`;
  }

  /**
   * Get hint text for number bonds
   */
  getNumberBondsHintText() {
    const { bondType, targetNumber, knownNumber, startNumber } = this.numberBondsState;

    switch (bondType) {
      case 'addition':
        return `Find the missing number to make ${targetNumber}`;
      case 'subtraction':
        return `Find what to subtract from ${startNumber} to get ${targetNumber}`;
      case 'decomposition':
        return `Find different ways to split ${targetNumber} into two numbers`;
      default:
        return 'Complete the number bond';
    }
  }

  /**
   * Set up number bonds controls
   */
  setupNumberBondsControls() {
    const clearBtn = document.getElementById('clear-bonds-btn');
    const hintBtn = document.getElementById('hint-bonds-btn');
    const checkBtn = document.getElementById('check-bond-btn');

    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearNumberBonds();
      });
    }

    if (hintBtn) {
      hintBtn.addEventListener('click', () => {
        this.showNumberBondsHint();
      });
    }

    if (checkBtn) {
      checkBtn.addEventListener('click', () => {
        this.checkNumberBondsAnswer();
      });
    }

    // Add enter key support for inputs
    const inputs = document.querySelectorAll('.bond-input');
    inputs.forEach(input => {
      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.checkNumberBondsAnswer();
        }
      });
    });

    // Store cleanup function
    this.numberBondsCleanup = () => {
      if (clearBtn) clearBtn.removeEventListener('click', this.clearNumberBonds);
      if (hintBtn) hintBtn.removeEventListener('click', this.showNumberBondsHint);
      if (checkBtn) checkBtn.removeEventListener('click', this.checkNumberBondsAnswer);

      inputs.forEach(input => {
        input.removeEventListener('keypress', this.checkNumberBondsAnswer);
      });
    };
  }

  /**
   * Check number bonds answer
   */
  checkNumberBondsAnswer() {
    const { bondType } = this.numberBondsState;

    // Log attempt for performance tracking
    this.logInteraction('attempt', {
      bondType: bondType,
      attemptNumber: this.currentQuestionPerformance.attempts + 1
    });

    switch (bondType) {
      case 'addition':
        this.checkAdditionBond();
        break;
      case 'subtraction':
        this.checkSubtractionBond();
        break;
      case 'decomposition':
        this.checkDecompositionBond();
        break;
    }
  }

  /**
   * Check addition bond answer
   */
  checkAdditionBond() {
    const input = document.getElementById('missing-number-input');
    const userAnswer = parseInt(input.value);
    const { missingNumber } = this.numberBondsState;

    if (isNaN(userAnswer)) {
      this.showBondFeedback('Please enter a number', 'error');
      return;
    }

    if (userAnswer === missingNumber) {
      this.showBondFeedback('Correct! Well done.', 'success');
      this.numberBondsState.isComplete = true;
      this.numberBondsState.userAnswers = [userAnswer];
      this.checkNumberBondsCompletion();
    } else {
      this.showBondFeedback('Not quite right. Try again.', 'error');
    }
  }

  /**
   * Check subtraction bond answer
   */
  checkSubtractionBond() {
    const input = document.getElementById('missing-number-input');
    const userAnswer = parseInt(input.value);
    const { missingNumber } = this.numberBondsState;

    if (isNaN(userAnswer)) {
      this.showBondFeedback('Please enter a number', 'error');
      return;
    }

    if (userAnswer === missingNumber) {
      this.showBondFeedback('Correct! Well done.', 'success');
      this.numberBondsState.isComplete = true;
      this.numberBondsState.userAnswers = [userAnswer];
      this.checkNumberBondsCompletion();
    } else {
      this.showBondFeedback('Not quite right. Try again.', 'error');
    }
  }

  /**
   * Check decomposition bond answer
   */
  checkDecompositionBond() {
    const firstInput = document.getElementById('first-number-input');
    const secondInput = document.getElementById('second-number-input');
    const first = parseInt(firstInput.value);
    const second = parseInt(secondInput.value);
    const { targetNumber, correctPairs, allowMultiple } = this.numberBondsState;

    if (isNaN(first) || isNaN(second)) {
      this.showBondFeedback('Please enter both numbers', 'error');
      return;
    }

    if (first + second !== targetNumber) {
      this.showBondFeedback(`${first} + ${second} = ${first + second}, not ${targetNumber}. Try again.`, 'error');
      return;
    }

    // Check if this pair is correct
    const isValidPair = correctPairs.some(pair =>
      (pair.a === first && pair.b === second) || (pair.a === second && pair.b === first)
    );

    if (isValidPair) {
      // Check if already found
      const alreadyFound = this.numberBondsState.userAnswers.some(answer =>
        (answer.a === first && answer.b === second) || (answer.a === second && answer.b === first)
      );

      if (alreadyFound) {
        this.showBondFeedback('You already found this pair. Try another one.', 'warning');
      } else {
        this.showBondFeedback('Correct! Great work.', 'success');
        this.numberBondsState.userAnswers.push({ a: first, b: second });
        this.addFoundPair(first, second);

        // Clear inputs for next attempt
        firstInput.value = '';
        secondInput.value = '';

        if (!allowMultiple || this.numberBondsState.userAnswers.length >= correctPairs.length) {
          this.numberBondsState.isComplete = true;
        }

        this.checkNumberBondsCompletion();
      }
    } else {
      this.showBondFeedback('This pair adds up correctly but is not one of the expected answers.', 'warning');
    }
  }

  /**
   * Show feedback for number bonds
   */
  showBondFeedback(message, type) {
    // Create or update feedback display
    let feedbackDisplay = document.getElementById('bonds-feedback-display');
    if (!feedbackDisplay) {
      feedbackDisplay = document.createElement('div');
      feedbackDisplay.id = 'bonds-feedback-display';
      feedbackDisplay.className = 'bonds-feedback-display';

      const container = document.getElementById('number-bonds');
      container.appendChild(feedbackDisplay);
    }

    feedbackDisplay.className = `bonds-feedback-display ${type}`;
    feedbackDisplay.textContent = message;
    feedbackDisplay.style.display = 'block';

    // Auto-hide after 3 seconds for non-success messages
    if (type !== 'success') {
      setTimeout(() => {
        feedbackDisplay.style.display = 'none';
      }, 3000);
    }
  }

  /**
   * Add found pair to the list
   */
  addFoundPair(first, second) {
    const pairsList = document.getElementById('pairs-list');
    if (pairsList) {
      const pairElement = document.createElement('div');
      pairElement.className = 'found-pair';
      pairElement.textContent = `${first} + ${second}`;
      pairsList.appendChild(pairElement);
    }
  }

  /**
   * Clear number bonds
   */
  clearNumberBonds() {
    // Clear all inputs
    const inputs = document.querySelectorAll('.bond-input');
    inputs.forEach(input => {
      input.value = '';
    });

    // Clear found pairs
    const pairsList = document.getElementById('pairs-list');
    if (pairsList) {
      pairsList.innerHTML = '';
    }

    // Clear feedback
    const feedbackDisplay = document.getElementById('bonds-feedback-display');
    if (feedbackDisplay) {
      feedbackDisplay.style.display = 'none';
    }

    // Reset state
    this.numberBondsState.userAnswers = [];
    this.numberBondsState.isComplete = false;

    // Disable next button
    document.getElementById('next-question-btn').disabled = true;

    console.log('Number bonds cleared');
  }

  /**
   * Show number bonds hint
   */
  showNumberBondsHint() {
    // Log hint request for performance tracking
    this.logInteraction('hint_request', {
      questionType: 'number-bonds',
      bondType: this.numberBondsState.bondType,
      currentAttempts: this.currentQuestionPerformance.attempts
    });

    const { hint } = this.numberBondsState;
    const hintText = hint || this.getNumberBondsHintText();

    // Create or update hint display
    let hintDisplay = document.getElementById('bonds-hint-display');
    if (!hintDisplay) {
      hintDisplay = document.createElement('div');
      hintDisplay.id = 'bonds-hint-display';
      hintDisplay.className = 'bonds-hint-display';
      hintDisplay.style.cssText = `
        background: #fff3cd;
        color: #856404;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #ffeaa7;
        margin-top: 15px;
        font-style: italic;
        text-align: center;
      `;

      const container = document.getElementById('number-bonds');
      container.appendChild(hintDisplay);
    }

    hintDisplay.textContent = `💡 Hint: ${hintText}`;
    hintDisplay.style.display = 'block';

    console.log('Number bonds hint shown:', hintText);
  }

  /**
   * Check number bonds completion
   */
  checkNumberBondsCompletion() {
    const hasAnswer = this.numberBondsState.userAnswers.length > 0;
    const isComplete = this.numberBondsState.isComplete;

    // Enable next button if there's any correct answer
    document.getElementById('next-question-btn').disabled = !hasAnswer;

    if (isComplete) {
      this.showCompletionIndicator();
      this.clearFallbackTimer();

      // Finalize performance tracking
      this.finalizeQuestionPerformance(true);
    }

    console.log('Number bonds completion check:', { hasAnswer, isComplete });
  }

  /**
   * Get number bonds answer
   */
  getNumberBondsAnswer() {
    if (!this.numberBondsState) {
      return JSON.stringify({
        type: 'number-bonds',
        userAnswers: [],
        isComplete: false,
        error: 'No number bonds state found'
      });
    }

    const {
      bondType,
      targetNumber,
      userAnswers,
      isComplete,
      knownNumber,
      startNumber
    } = this.numberBondsState;

    // Get performance data for this question
    const performanceData = this.currentQuestionPerformance || {};
    const questionIndex = this.currentQuestionIndex;
    const interactionLogs = this.interactionLogs[questionIndex] || [];

    return JSON.stringify({
      type: 'number-bonds',
      bondType: bondType,
      targetNumber: targetNumber,
      knownNumber: knownNumber,
      startNumber: startNumber,
      userAnswers: userAnswers,
      isComplete: isComplete,
      timestamp: new Date().toISOString(),

      // Enhanced performance data
      performanceMetrics: {
        timeSpent: performanceData.completionTime ?
          performanceData.completionTime - performanceData.startTime : null,
        firstInteractionTime: performanceData.firstInteractionTime ?
          performanceData.firstInteractionTime - performanceData.startTime : null,
        totalInteractions: performanceData.difficultyIndicators?.totalInteractions || 0,
        attempts: performanceData.attempts || 0,
        hintsUsed: performanceData.hintsUsed || 0,
        errorsCount: performanceData.errorsCount || 0,
        completionEfficiency: performanceData.difficultyIndicators?.completionEfficiency || 0,
        errorRate: performanceData.difficultyIndicators?.errorRate || 0,
        hintDependency: performanceData.difficultyIndicators?.hintDependency || false
      },

      // Number bonds specific metrics
      numberBondsMetrics: {
        bondComplexity: bondType === 'decomposition' ? 'high' : 'medium',
        correctAnswersFound: userAnswers.length,
        averageResponseTime: performanceData.attempts > 0 ?
          (performanceData.completionTime - performanceData.startTime) / performanceData.attempts : 0,
        visualStyleUsed: this.numberBondsState.visualStyle,
        numberRange: Math.max(targetNumber || 0, knownNumber || 0, startNumber || 0)
      },

      // Detailed interaction logs
      interactionSequence: interactionLogs.map(log => ({
        type: log.type,
        timestamp: log.timestamp,
        timeFromStart: log.timeFromStart,
        details: log.details
      })),

      // Solution path analysis
      solutionPath: performanceData.solutionPath || [],

      // Mathematical reasoning indicators
      reasoningMetrics: {
        strategicApproach: performanceData.attempts <= 2 && isComplete,
        conceptualUnderstanding: performanceData.errorsCount === 0 && performanceData.hintsUsed === 0,
        numberSenseFluency: performanceData.firstInteractionTime < 5000, // Less than 5 seconds to start
        persistenceLevel: Math.min(performanceData.attempts / 3, 1) // Normalized persistence score
      }
    });
  }

  /**
   * Show step-by-step guided problems
   */
  showStepByStepGuided(question) {
    const container = document.getElementById('step-by-step-guided');

    // Get configuration from question
    const config = question.config || {};
    const { steps = [], finalAnswer = '' } = config;

    // Initialize step-by-step state
    this.stepByStepState = {
      steps: steps,
      currentStep: 0,
      finalAnswer: finalAnswer,
      stepAnswers: [],
      isComplete: false
    };

    // Set up the step display
    this.setupStepByStepDisplay();

    // Set up event listeners
    this.setupStepByStepInteraction();

    // Show the container
    container.classList.remove('hidden');

    console.log('Step-by-step guided problems initialized:', { steps: steps.length });
  }

  /**
   * Set up step-by-step display elements
   */
  setupStepByStepDisplay() {
    const instructionElement = document.getElementById('step-instruction');
    const stepNumberElement = document.getElementById('current-step-number');
    const totalStepsElement = document.getElementById('total-steps');
    const stepInput = document.getElementById('step-answer');
    const feedbackElement = document.getElementById('step-feedback');
    const hintElement = document.getElementById('step-hint');

    // Update step information
    const { steps, currentStep } = this.stepByStepState;
    const step = steps[currentStep];

    if (step) {
      instructionElement.textContent = step.instruction;
      stepNumberElement.textContent = currentStep + 1;
      totalStepsElement.textContent = steps.length;

      // Clear previous input and feedback
      stepInput.value = '';
      feedbackElement.classList.add('hidden');
      hintElement.classList.add('hidden');

      // Focus on input
      stepInput.focus();
    }
  }

  /**
   * Set up step-by-step interaction handlers
   */
  setupStepByStepInteraction() {
    const checkBtn = document.getElementById('check-step-btn');
    const stepInput = document.getElementById('step-answer');

    // Verify elements exist
    if (!checkBtn || !stepInput) {
      console.error('Step-by-step elements not found:', { checkBtn: !!checkBtn, stepInput: !!stepInput });
      return;
    }

    // Check step button handler
    const handleCheckStep = () => {
      console.log('Check step button clicked');
      this.checkCurrentStep();
    };

    // Enter key handler for input
    const handleInputKeyPress = (e) => {
      if (e.key === 'Enter') {
        console.log('Enter key pressed in step input');
        this.checkCurrentStep();
      }
    };

    // Input change handler to enable next button
    const handleInputChange = () => {
      const hasInput = stepInput.value.trim().length > 0;
      if (hasInput) {
        // Enable next button if user has entered something
        const nextBtn = document.getElementById('next-question-btn');
        if (nextBtn) {
          nextBtn.disabled = false;
        }
      }
    };

    // Add event listeners
    checkBtn.addEventListener('click', handleCheckStep);
    stepInput.addEventListener('keypress', handleInputKeyPress);
    stepInput.addEventListener('input', handleInputChange);

    // Store cleanup function
    this.stepByStepCleanup = () => {
      checkBtn.removeEventListener('click', handleCheckStep);
      stepInput.removeEventListener('keypress', handleInputKeyPress);
      stepInput.removeEventListener('input', handleInputChange);
    };

    console.log('Step-by-step interaction handlers set up successfully');
  }

  /**
   * Check the current step answer
   */
  checkCurrentStep() {
    const stepInput = document.getElementById('step-answer');
    const feedbackElement = document.getElementById('step-feedback');
    const hintElement = document.getElementById('step-hint');

    // Verify state and elements exist
    if (!this.stepByStepState || !stepInput) {
      console.error('Step-by-step state or input element not found');
      return;
    }

    const { steps, currentStep } = this.stepByStepState;
    const step = steps[currentStep];

    if (!step) {
      console.error('Current step not found:', { currentStep, totalSteps: steps.length });
      return;
    }

    const userAnswer = stepInput.value.trim();

    if (!userAnswer) {
      this.showStepFeedback('Please enter an answer', 'incorrect');
      return;
    }

    console.log('Checking step answer:', { userAnswer, expectedAnswer: step.expectedAnswer });

    // Store the user's answer
    this.stepByStepState.stepAnswers[currentStep] = userAnswer;

    // Check if answer is correct (case-insensitive, flexible matching)
    const isCorrect = this.checkStepAnswer(userAnswer, step.expectedAnswer);

    if (isCorrect) {
      this.showStepFeedback('Correct! Well done.', 'correct');

      // Move to next step after a short delay
      setTimeout(() => {
        this.moveToNextStep();
      }, 1500);
    } else {
      this.showStepFeedback('Not quite right. Try again.', 'incorrect');

      // Show hint if available
      if (step.hint) {
        setTimeout(() => {
          this.showStepHint(step.hint);
        }, 1000);
      }
    }

    // Always enable next button after user attempts an answer
    const nextBtn = document.getElementById('next-question-btn');
    if (nextBtn) {
      nextBtn.disabled = false;
    }
  }

  /**
   * Check if step answer is correct with flexible matching
   */
  checkStepAnswer(userAnswer, expectedAnswer) {
    // Normalize both answers for comparison
    const normalize = (str) => str.toLowerCase().replace(/\s+/g, '').replace(/[^\w=+\-*/().]/g, '');

    const normalizedUser = normalize(userAnswer);
    const normalizedExpected = normalize(expectedAnswer);

    // Direct match
    if (normalizedUser === normalizedExpected) {
      return true;
    }

    // Try to evaluate as mathematical expressions if they contain numbers
    try {
      if (/\d/.test(userAnswer) && /\d/.test(expectedAnswer)) {
        const userValue = this.evaluateSimpleExpression(userAnswer);
        const expectedValue = this.evaluateSimpleExpression(expectedAnswer);

        if (userValue !== null && expectedValue !== null) {
          return Math.abs(userValue - expectedValue) < 0.001; // Allow small floating point differences
        }
      }
    } catch (e) {
      // If evaluation fails, fall back to string comparison
    }

    return false;
  }

  /**
   * Safely evaluate simple mathematical expressions
   */
  evaluateSimpleExpression(expr) {
    try {
      // Only allow safe mathematical operations
      const sanitized = expr.replace(/[^0-9+\-*/.() ]/g, '');
      if (sanitized !== expr.replace(/\s/g, '')) {
        return null; // Contains unsafe characters
      }

      // Use Function constructor for safer evaluation than eval
      return new Function('return ' + sanitized)();
    } catch (e) {
      return null;
    }
  }

  /**
   * Show step feedback
   */
  showStepFeedback(message, type) {
    const feedbackElement = document.getElementById('step-feedback');

    feedbackElement.textContent = message;
    feedbackElement.className = `step-feedback ${type}`;
    feedbackElement.classList.remove('hidden');
  }

  /**
   * Show step hint
   */
  showStepHint(hint) {
    const hintElement = document.getElementById('step-hint');

    hintElement.textContent = `Hint: ${hint}`;
    hintElement.classList.remove('hidden');
  }

  /**
   * Move to the next step or complete the problem
   */
  moveToNextStep() {
    const { steps, currentStep } = this.stepByStepState;

    if (currentStep < steps.length - 1) {
      // Move to next step
      this.stepByStepState.currentStep++;
      this.setupStepByStepDisplay();
    } else {
      // All steps completed
      this.completeStepByStepProblem();
    }
  }

  /**
   * Complete the step-by-step problem
   */
  completeStepByStepProblem() {
    this.stepByStepState.isComplete = true;

    // Show completion message
    const instructionElement = document.getElementById('step-instruction');
    const stepInput = document.getElementById('step-answer');
    const checkBtn = document.getElementById('check-step-btn');

    instructionElement.textContent = 'Great job! You have completed all steps.';
    stepInput.style.display = 'none';
    checkBtn.style.display = 'none';

    this.showStepFeedback(`Final answer: ${this.stepByStepState.finalAnswer}`, 'correct');

    // Enable next question button and show completion
    document.getElementById('next-question-btn').disabled = false;
    this.showCompletionIndicator();
    this.clearFallbackTimer();

    console.log('Step-by-step problem completed');
  }

  /**
   * Get step-by-step answer
   */
  getStepByStepAnswer() {
    if (!this.stepByStepState) {
      return JSON.stringify({
        type: 'step-by-step',
        stepAnswers: [],
        isComplete: false,
        error: 'No step-by-step state found'
      });
    }

    const { stepAnswers, isComplete, finalAnswer, steps, currentStep } = this.stepByStepState;

    // Get performance data for this question
    const performanceData = this.currentQuestionPerformance || {};
    const questionIndex = this.currentQuestionIndex;
    const interactionLogs = this.interactionLogs[questionIndex] || [];

    return JSON.stringify({
      type: 'step-by-step',
      stepAnswers: stepAnswers || [],
      isComplete: isComplete || false,
      finalAnswer: finalAnswer || '',
      currentStep: currentStep || 0,
      totalSteps: steps ? steps.length : 0,
      completedSteps: stepAnswers ? stepAnswers.length : 0,
      progress: steps && steps.length > 0 ? (stepAnswers.length / steps.length) * 100 : 0,
      timestamp: new Date().toISOString(),

      // Enhanced performance data
      performanceMetrics: {
        timeSpent: performanceData.completionTime ?
          performanceData.completionTime - performanceData.startTime : null,
        firstInteractionTime: performanceData.firstInteractionTime ?
          performanceData.firstInteractionTime - performanceData.startTime : null,
        totalInteractions: performanceData.difficultyIndicators?.totalInteractions || 0,
        attempts: performanceData.attempts || 0,
        hintsUsed: performanceData.hintsUsed || 0,
        errorsCount: performanceData.errorsCount || 0,
        completionEfficiency: performanceData.difficultyIndicators?.completionEfficiency || 0,
        stepCompletionRate: steps && steps.length > 0 ? (stepAnswers?.length || 0) / steps.length : 0
      },

      // Step-by-step specific metrics
      stepByStepMetrics: {
        averageTimePerStep: steps && steps.length > 0 && performanceData.completionTime ?
          (performanceData.completionTime - performanceData.startTime) / steps.length : 0,
        stepsWithErrors: interactionLogs.filter(log => log.type === 'error').length,
        stepsWithHints: interactionLogs.filter(log => log.type === 'hint_request').length,
        sequentialProgress: stepAnswers ? stepAnswers.every((answer, index) => answer !== null) : false,
        problemSolvingStrategy: this.analyzeStepByStepStrategy(stepAnswers, steps)
      },

      // Detailed interaction logs
      interactionSequence: interactionLogs.map(log => ({
        type: log.type,
        timestamp: log.timestamp,
        timeFromStart: log.timeFromStart,
        details: log.details
      })),

      // Solution path analysis
      solutionPath: performanceData.solutionPath || [],

      // Learning progression indicators
      learningMetrics: {
        conceptualUnderstanding: performanceData.errorsCount === 0 && performanceData.hintsUsed <= 1,
        proceduralFluency: performanceData.completionTime && performanceData.completionTime - performanceData.startTime < 300000, // 5 minutes
        metacognitiveSelfRegulation: performanceData.hintsUsed > 0 && isComplete,
        persistenceAndEngagement: performanceData.attempts >= 2 && isComplete
      }
    });
  }

  /**
   * Analyze step-by-step problem solving strategy
   */
  analyzeStepByStepStrategy(stepAnswers, steps) {
    if (!stepAnswers || !steps || stepAnswers.length === 0) {
      return 'incomplete';
    }

    const completedSteps = stepAnswers.filter(answer => answer !== null && answer !== '').length;
    const totalSteps = steps.length;

    if (completedSteps === totalSteps) {
      return 'systematic_complete';
    } else if (completedSteps > totalSteps * 0.7) {
      return 'mostly_systematic';
    } else if (completedSteps > totalSteps * 0.3) {
      return 'partial_systematic';
    } else {
      return 'struggling';
    }
  }

  /**
   * Show coordinate plotting using HTML5 Canvas
   */
  showCoordinatePlotting(question) {
    const container = document.getElementById('coordinate-plotting');
    const canvas = document.getElementById('coordinate-canvas');

    // Get configuration from question
    const config = question.config || {};
    const {
      gridSize = { width: 10, height: 10 },
      origin = { x: 5, y: 5 },
      correctPoints = [],
      showGrid = true,
      showAxes = true,
      drawLine = false,
      drawCurve = false
    } = config;

    // Set up canvas
    canvas.width = 400;
    canvas.height = 400;
    const ctx = canvas.getContext('2d');

    // Initialize coordinate plotting state
    this.coordinatePlottingState = {
      canvas: canvas,
      ctx: ctx,
      gridSize: gridSize,
      origin: origin,
      correctPoints: correctPoints,
      plottedPoints: [],
      showGrid: showGrid,
      showAxes: showAxes,
      drawLine: drawLine,
      drawCurve: drawCurve,
      scale: {
        x: canvas.width / gridSize.width,
        y: canvas.height / gridSize.height
      }
    };

    // Set up canvas interaction
    this.setupCoordinatePlottingInteraction();

    // Draw initial grid
    this.drawCoordinateGrid();

    // Set up controls
    this.setupCoordinatePlottingControls();

    // Show the container
    container.classList.remove('hidden');

    console.log('Coordinate plotting initialized:', { gridSize, origin });
  }

  /**
   * Draw the coordinate grid and axes
   */
  drawCoordinateGrid() {
    const { ctx, canvas, gridSize, origin, showGrid, showAxes, scale } = this.coordinatePlottingState;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set up drawing styles
    ctx.strokeStyle = '#e0e6ff';
    ctx.lineWidth = 1;

    // Draw grid lines if enabled
    if (showGrid) {
      // Vertical grid lines
      for (let i = 0; i <= gridSize.width; i++) {
        const x = i * scale.x;
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
      }

      // Horizontal grid lines
      for (let i = 0; i <= gridSize.height; i++) {
        const y = i * scale.y;
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }
    }

    // Draw axes if enabled
    if (showAxes) {
      ctx.strokeStyle = '#1547bb';
      ctx.lineWidth = 2;

      // X-axis
      const yAxisPos = (gridSize.height - origin.y) * scale.y;
      ctx.beginPath();
      ctx.moveTo(0, yAxisPos);
      ctx.lineTo(canvas.width, yAxisPos);
      ctx.stroke();

      // Y-axis
      const xAxisPos = origin.x * scale.x;
      ctx.beginPath();
      ctx.moveTo(xAxisPos, 0);
      ctx.lineTo(xAxisPos, canvas.height);
      ctx.stroke();

      // Draw axis labels
      this.drawAxisLabels();
    }

    // Draw plotted points
    this.drawPlottedPoints();

    // Draw line or curve if needed
    if (this.coordinatePlottingState.drawLine && this.coordinatePlottingState.plottedPoints.length >= 2) {
      this.drawConnectingLine();
    }
  }

  /**
   * Draw axis labels
   */
  drawAxisLabels() {
    const { ctx, gridSize, origin, scale } = this.coordinatePlottingState;

    ctx.fillStyle = '#121c41';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    // X-axis labels
    const yAxisPos = (gridSize.height - origin.y) * scale.y;
    for (let i = 0; i <= gridSize.width; i++) {
      const x = i * scale.x;
      const value = i - origin.x;
      if (value !== 0) { // Don't label the origin
        ctx.fillText(value.toString(), x, yAxisPos + 15);
      }
    }

    // Y-axis labels
    const xAxisPos = origin.x * scale.x;
    ctx.textAlign = 'right';
    for (let i = 0; i <= gridSize.height; i++) {
      const y = i * scale.y;
      const value = origin.y - i;
      if (value !== 0) { // Don't label the origin
        ctx.fillText(value.toString(), xAxisPos - 5, y + 4);
      }
    }

    // Origin label
    ctx.textAlign = 'right';
    ctx.fillText('0', xAxisPos - 5, yAxisPos + 15);
  }

  /**
   * Draw plotted points
   */
  drawPlottedPoints() {
    const { ctx, plottedPoints } = this.coordinatePlottingState;

    ctx.fillStyle = '#1547bb';

    plottedPoints.forEach((point, index) => {
      const canvasPos = this.mathToCanvas(point.x, point.y);

      // Draw point
      ctx.beginPath();
      ctx.arc(canvasPos.x, canvasPos.y, 4, 0, 2 * Math.PI);
      ctx.fill();

      // Draw point label
      ctx.fillStyle = '#121c41';
      ctx.font = '10px Arial';
      ctx.textAlign = 'left';
      ctx.fillText(`(${point.x}, ${point.y})`, canvasPos.x + 6, canvasPos.y - 6);
      ctx.fillStyle = '#1547bb';
    });
  }

  /**
   * Draw connecting line between points
   */
  drawConnectingLine() {
    const { ctx, plottedPoints } = this.coordinatePlottingState;

    if (plottedPoints.length < 2) return;

    ctx.strokeStyle = '#1547bb';
    ctx.lineWidth = 2;
    ctx.beginPath();

    const firstPoint = this.mathToCanvas(plottedPoints[0].x, plottedPoints[0].y);
    ctx.moveTo(firstPoint.x, firstPoint.y);

    for (let i = 1; i < plottedPoints.length; i++) {
      const point = this.mathToCanvas(plottedPoints[i].x, plottedPoints[i].y);
      ctx.lineTo(point.x, point.y);
    }

    ctx.stroke();
  }

  /**
   * Convert mathematical coordinates to canvas coordinates
   */
  mathToCanvas(mathX, mathY) {
    const { origin, scale } = this.coordinatePlottingState;

    return {
      x: (mathX + origin.x) * scale.x,
      y: (origin.y - mathY) * scale.y
    };
  }

  /**
   * Convert canvas coordinates to mathematical coordinates
   */
  canvasToMath(canvasX, canvasY) {
    const { origin, scale } = this.coordinatePlottingState;

    return {
      x: Math.round((canvasX / scale.x) - origin.x),
      y: Math.round(origin.y - (canvasY / scale.y))
    };
  }

  /**
   * Set up coordinate plotting interaction
   */
  setupCoordinatePlottingInteraction() {
    const canvas = this.coordinatePlottingState.canvas;

    const handleCanvasClick = (e) => {
      const rect = canvas.getBoundingClientRect();
      const canvasX = e.clientX - rect.left;
      const canvasY = e.clientY - rect.top;

      this.handleCoordinatePlottingClick(canvasX, canvasY);
    };

    const handleCanvasMouseMove = (e) => {
      const rect = canvas.getBoundingClientRect();
      const canvasX = e.clientX - rect.left;
      const canvasY = e.clientY - rect.top;

      this.updateCoordinateDisplay(canvasX, canvasY);
    };

    canvas.addEventListener('click', handleCanvasClick);
    canvas.addEventListener('mousemove', handleCanvasMouseMove);

    // Store cleanup function
    this.coordinatePlottingCleanup = () => {
      canvas.removeEventListener('click', handleCanvasClick);
      canvas.removeEventListener('mousemove', handleCanvasMouseMove);
    };
  }

  /**
   * Handle click on coordinate plotting canvas
   */
  handleCoordinatePlottingClick(canvasX, canvasY) {
    const mathCoords = this.canvasToMath(canvasX, canvasY);

    // Check if point already exists at this location
    const existingPointIndex = this.coordinatePlottingState.plottedPoints.findIndex(
      point => point.x === mathCoords.x && point.y === mathCoords.y
    );

    if (existingPointIndex >= 0) {
      // Remove existing point
      this.coordinatePlottingState.plottedPoints.splice(existingPointIndex, 1);
    } else {
      // Add new point
      this.coordinatePlottingState.plottedPoints.push(mathCoords);
    }

    // Redraw grid
    this.drawCoordinateGrid();

    // Check completion
    this.checkCoordinatePlottingCompletion();

    console.log('Point plotted:', mathCoords, 'Total points:', this.coordinatePlottingState.plottedPoints.length);
  }

  /**
   * Update coordinate display
   */
  updateCoordinateDisplay(canvasX, canvasY) {
    const mathCoords = this.canvasToMath(canvasX, canvasY);
    const display = document.getElementById('current-coordinates');

    if (display) {
      display.textContent = `(${mathCoords.x}, ${mathCoords.y})`;
    }
  }

  /**
   * Set up coordinate plotting controls
   */
  setupCoordinatePlottingControls() {
    const clearBtn = document.getElementById('clear-points-btn');

    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearCoordinatePlottingPoints();
      });
    }
  }

  /**
   * Clear all plotted points
   */
  clearCoordinatePlottingPoints() {
    this.coordinatePlottingState.plottedPoints = [];
    this.drawCoordinateGrid();
    this.checkCoordinatePlottingCompletion();

    console.log('All points cleared');
  }

  /**
   * Check if coordinate plotting has any points
   */
  checkCoordinatePlottingCompletion() {
    const hasPoints = this.coordinatePlottingState.plottedPoints.length > 0;
    document.getElementById('next-question-btn').disabled = !hasPoints;
  }

  /**
   * Get coordinate plotting answer
   */
  getCoordinatePlottingAnswer() {
    return JSON.stringify({
      plottedPoints: this.coordinatePlottingState.plottedPoints
    });
  }

  /**
   * Show ratio sliders
   */
  showRatioSliders(question) {
    const container = document.getElementById('ratio-sliders');
    const slidersContainer = document.getElementById('ratio-sliders-container');

    // Get configuration from question
    const config = question.config || {};
    const {
      sliders = [],
      correctRatio = {},
      tolerance = 0.1,
      ratioBase = null
    } = config;

    // Clear container
    slidersContainer.innerHTML = '';

    // Initialize ratio sliders state
    this.ratioSlidersState = {
      sliders: sliders,
      correctRatio: correctRatio,
      tolerance: tolerance,
      ratioBase: ratioBase,
      currentValues: {}
    };

    // Create slider elements
    this.createRatioSliders();

    // Set up interaction
    this.setupRatioSlidersInteraction();

    // Set up controls
    this.setupRatioSlidersControls();

    // Initial display update
    this.updateRatioDisplay();

    // Show the container
    container.classList.remove('hidden');

    console.log('Ratio sliders initialized:', { sliders: sliders.length });
  }

  /**
   * Create ratio slider elements
   */
  createRatioSliders() {
    const slidersContainer = document.getElementById('ratio-sliders-container');
    const { sliders } = this.ratioSlidersState;

    sliders.forEach((slider, index) => {
      const { id, label, min, max, step, fixed } = slider;

      // Create slider group
      const sliderGroup = document.createElement('div');
      sliderGroup.className = 'ratio-slider-group';

      // Create label
      const labelElement = document.createElement('label');
      labelElement.className = 'ratio-slider-label';
      labelElement.textContent = label;
      labelElement.setAttribute('for', `ratio-slider-${id}`);

      // Create slider input
      const sliderInput = document.createElement('input');
      sliderInput.type = 'range';
      sliderInput.id = `ratio-slider-${id}`;
      sliderInput.className = 'ratio-slider';
      sliderInput.min = min;
      sliderInput.max = max;
      sliderInput.step = step || 1;
      sliderInput.value = fixed || min;
      sliderInput.dataset.sliderId = id;

      // Disable if fixed value
      if (fixed !== undefined) {
        sliderInput.disabled = true;
      }

      // Create value display
      const valueDisplay = document.createElement('span');
      valueDisplay.className = 'ratio-value-display';
      valueDisplay.id = `ratio-value-${id}`;
      valueDisplay.textContent = fixed || min;

      // Assemble slider group
      sliderGroup.appendChild(labelElement);
      sliderGroup.appendChild(sliderInput);
      sliderGroup.appendChild(valueDisplay);

      slidersContainer.appendChild(sliderGroup);

      // Store initial value
      this.ratioSlidersState.currentValues[id] = parseFloat(fixed || min);
    });
  }

  /**
   * Set up ratio sliders interaction
   */
  setupRatioSlidersInteraction() {
    const sliderInputs = document.querySelectorAll('.ratio-slider');

    const handleSliderChange = (e) => {
      const sliderId = e.target.dataset.sliderId;
      const value = parseFloat(e.target.value);

      // Update state
      this.ratioSlidersState.currentValues[sliderId] = value;

      // Update value display
      const valueDisplay = document.getElementById(`ratio-value-${sliderId}`);
      if (valueDisplay) {
        valueDisplay.textContent = value;
      }

      // Update ratio display
      this.updateRatioDisplay();

      // Check completion
      this.checkRatioSlidersCompletion();

      console.log('Ratio slider changed:', { sliderId, value });
    };

    sliderInputs.forEach(slider => {
      slider.addEventListener('input', handleSliderChange);
    });

    // Store cleanup function
    this.ratioSlidersCleanup = () => {
      sliderInputs.forEach(slider => {
        slider.removeEventListener('input', handleSliderChange);
      });
    };
  }

  /**
   * Update ratio display
   */
  updateRatioDisplay() {
    const ratioDisplay = document.getElementById('current-ratio');
    const { sliders, currentValues } = this.ratioSlidersState;

    if (!ratioDisplay || sliders.length === 0) return;

    // Get values in order of sliders
    const values = sliders.map(slider => currentValues[slider.id] || 0);

    // Create ratio string
    const ratioString = values.join(':');
    ratioDisplay.textContent = ratioString;

    // Simplify ratio if possible
    const gcd = this.findGCD(values);
    if (gcd > 1) {
      const simplifiedValues = values.map(v => v / gcd);
      const simplifiedString = simplifiedValues.join(':');
      ratioDisplay.textContent = `${ratioString} (${simplifiedString})`;
    }
  }

  /**
   * Find Greatest Common Divisor of an array of numbers
   */
  findGCD(numbers) {
    const gcdTwo = (a, b) => {
      while (b !== 0) {
        const temp = b;
        b = a % b;
        a = temp;
      }
      return a;
    };

    return numbers.reduce((acc, num) => gcdTwo(acc, Math.abs(num)));
  }

  /**
   * Set up ratio sliders controls
   */
  setupRatioSlidersControls() {
    const resetBtn = document.getElementById('reset-ratio-btn');

    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        this.resetRatioSliders();
      });
    }
  }

  /**
   * Reset ratio sliders to initial values
   */
  resetRatioSliders() {
    const { sliders } = this.ratioSlidersState;

    sliders.forEach(slider => {
      const { id, min, fixed } = slider;
      const initialValue = fixed || min;

      // Update slider input
      const sliderInput = document.getElementById(`ratio-slider-${id}`);
      if (sliderInput && !sliderInput.disabled) {
        sliderInput.value = initialValue;
      }

      // Update value display
      const valueDisplay = document.getElementById(`ratio-value-${id}`);
      if (valueDisplay) {
        valueDisplay.textContent = initialValue;
      }

      // Update state
      this.ratioSlidersState.currentValues[id] = parseFloat(initialValue);
    });

    // Update displays
    this.updateRatioDisplay();
    this.checkRatioSlidersCompletion();

    console.log('Ratio sliders reset');
  }

  /**
   * Check if ratio sliders match the correct ratio
   */
  checkRatioSlidersCompletion() {
    const { correctRatio, tolerance, currentValues, ratioBase } = this.ratioSlidersState;

    let isCorrect = false;

    if (ratioBase) {
      // Check proportional ratio
      isCorrect = this.checkProportionalRatio(currentValues, correctRatio, ratioBase, tolerance);
    } else {
      // Check exact ratio match
      isCorrect = this.checkExactRatio(currentValues, correctRatio, tolerance);
    }

    // Enable next button if correct or allow partial completion
    document.getElementById('next-question-btn').disabled = false;

    // Provide visual feedback if correct
    const ratioDisplay = document.getElementById('current-ratio');
    if (ratioDisplay) {
      ratioDisplay.style.color = isCorrect ? '#28a745' : '#121c41';
      ratioDisplay.style.fontWeight = isCorrect ? 'bold' : 'normal';
    }

    console.log('Ratio check:', { isCorrect, currentValues, correctRatio });
  }

  /**
   * Check if current values match exact ratio
   */
  checkExactRatio(currentValues, correctRatio, tolerance) {
    for (const key in correctRatio) {
      const current = currentValues[key] || 0;
      const correct = correctRatio[key];

      if (Math.abs(current - correct) > tolerance) {
        return false;
      }
    }
    return true;
  }

  /**
   * Check if current values are proportional to the base ratio
   */
  checkProportionalRatio(currentValues, correctRatio, ratioBase, tolerance) {
    // Find the scaling factor from any non-zero base value
    let scaleFactor = null;

    for (const key in ratioBase) {
      if (ratioBase[key] !== 0 && currentValues[key] !== undefined) {
        scaleFactor = currentValues[key] / ratioBase[key];
        break;
      }
    }

    if (scaleFactor === null) return false;

    // Check if all values maintain the same scale factor
    for (const key in ratioBase) {
      const expectedValue = ratioBase[key] * scaleFactor;
      const actualValue = currentValues[key] || 0;

      if (Math.abs(actualValue - expectedValue) > tolerance) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get ratio sliders answer
   */
  getRatioSlidersAnswer() {
    return JSON.stringify({
      currentValues: this.ratioSlidersState.currentValues,
      ratioString: document.getElementById('current-ratio')?.textContent || ''
    });
  }

  /**
   * Show equation builders using drag and drop
   */
  showEquationBuilders(question) {
    const container = document.getElementById('equation-builders');
    const termsListContainer = document.getElementById('terms-list');
    const equationArea = document.getElementById('equation-area');

    // Get configuration from question
    const config = question.config || {};
    const {
      availableTerms = [],
      correctEquation = [],
      allowedOperations = ['+', '-', '×', '÷', '='],
      variables = ['x', 'y']
    } = config;

    // Clear containers
    termsListContainer.innerHTML = '';
    equationArea.innerHTML = '';

    // Initialize equation builder state
    this.equationBuilderState = {
      availableTerms: availableTerms,
      correctEquation: correctEquation,
      allowedOperations: allowedOperations,
      variables: variables,
      currentEquation: [],
      usedTerms: new Set()
    };

    // Create term elements
    this.createEquationTerms();

    // Set up drag and drop
    this.setupEquationBuilderDragDrop();

    // Set up controls
    this.setupEquationBuilderControls();

    // Show the container
    container.classList.remove('hidden');

    console.log('Equation builders initialized:', { availableTerms: availableTerms.length });
  }

  /**
   * Create equation term elements
   */
  createEquationTerms() {
    const termsListContainer = document.getElementById('terms-list');
    const { availableTerms } = this.equationBuilderState;

    availableTerms.forEach((term, index) => {
      const termElement = document.createElement('div');
      termElement.className = 'equation-term';
      termElement.textContent = term;
      termElement.dataset.termId = `term-${index}`;
      termElement.dataset.termValue = term;
      termElement.draggable = true;

      termsListContainer.appendChild(termElement);
    });
  }

  /**
   * Set up equation builder drag and drop using interact.js
   */
  setupEquationBuilderDragDrop() {
    // Make terms draggable
    interact('.equation-term')
      .draggable({
        inertia: true,
        modifiers: [
          interact.modifiers.restrictRect({
            restriction: 'parent',
            endOnly: true
          })
        ],
        autoScroll: true,
        listeners: {
          start: (event) => {
            event.target.classList.add('dragging');
          },
          move: (event) => {
            const target = event.target;
            const x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
            const y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

            target.style.transform = `translate(${x}px, ${y}px)`;
            target.setAttribute('data-x', x);
            target.setAttribute('data-y', y);
          },
          end: (event) => {
            event.target.classList.remove('dragging');
          }
        }
      });

    // Make equation area droppable
    interact('#equation-area')
      .dropzone({
        accept: '.equation-term',
        overlap: 0.5,
        listeners: {
          dragenter: (event) => {
            event.target.classList.add('drag-over');
          },
          dragleave: (event) => {
            event.target.classList.remove('drag-over');
          },
          drop: (event) => {
            const draggableElement = event.relatedTarget;
            const dropzoneElement = event.target;

            this.handleEquationTermDrop(draggableElement, dropzoneElement);
          }
        }
      });

    // Store cleanup function
    this.equationBuilderCleanup = () => {
      interact('.equation-term').unset();
      interact('#equation-area').unset();
    };
  }

  /**
   * Handle equation term drop
   */
  handleEquationTermDrop(termElement, equationArea) {
    const termValue = termElement.dataset.termValue;
    const termId = termElement.dataset.termId;

    // Remove drag-over styling
    equationArea.classList.remove('drag-over');

    // Check if term is already used
    if (this.equationBuilderState.usedTerms.has(termId)) {
      console.log('Term already used:', termValue);
      return;
    }

    // Add term to equation
    this.equationBuilderState.currentEquation.push(termValue);
    this.equationBuilderState.usedTerms.add(termId);

    // Create term copy in equation area
    const termCopy = document.createElement('div');
    termCopy.className = 'equation-term';
    termCopy.textContent = termValue;
    termCopy.dataset.termId = termId;
    termCopy.dataset.termValue = termValue;

    // Add remove functionality
    termCopy.addEventListener('click', () => {
      this.removeTermFromEquation(termId, termCopy);
    });

    equationArea.appendChild(termCopy);

    // Hide original term
    termElement.style.opacity = '0.3';
    termElement.style.pointerEvents = 'none';

    // Reset position
    termElement.style.transform = 'translate(0px, 0px)';
    termElement.setAttribute('data-x', 0);
    termElement.setAttribute('data-y', 0);

    // Check completion
    this.checkEquationBuilderCompletion();

    console.log('Term added to equation:', termValue, 'Current equation:', this.equationBuilderState.currentEquation);
  }

  /**
   * Remove term from equation
   */
  removeTermFromEquation(termId, termCopy) {
    const termValue = termCopy.dataset.termValue;

    // Remove from equation array
    const index = this.equationBuilderState.currentEquation.indexOf(termValue);
    if (index > -1) {
      this.equationBuilderState.currentEquation.splice(index, 1);
    }

    // Remove from used terms
    this.equationBuilderState.usedTerms.delete(termId);

    // Remove from equation area
    termCopy.remove();

    // Show original term again
    const originalTerm = document.querySelector(`[data-term-id="${termId}"]`);
    if (originalTerm && originalTerm !== termCopy) {
      originalTerm.style.opacity = '1';
      originalTerm.style.pointerEvents = 'auto';
    }

    // Check completion
    this.checkEquationBuilderCompletion();

    console.log('Term removed from equation:', termValue, 'Current equation:', this.equationBuilderState.currentEquation);
  }

  /**
   * Set up equation builder controls
   */
  setupEquationBuilderControls() {
    const clearBtn = document.getElementById('clear-equation-btn');

    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearEquationBuilder();
      });
    }
  }

  /**
   * Clear equation builder
   */
  clearEquationBuilder() {
    const equationArea = document.getElementById('equation-area');

    // Clear equation area
    equationArea.innerHTML = '';

    // Reset state
    this.equationBuilderState.currentEquation = [];
    this.equationBuilderState.usedTerms.clear();

    // Show all original terms
    const originalTerms = document.querySelectorAll('#terms-list .equation-term');
    originalTerms.forEach(term => {
      term.style.opacity = '1';
      term.style.pointerEvents = 'auto';
      term.style.transform = 'translate(0px, 0px)';
      term.setAttribute('data-x', 0);
      term.setAttribute('data-y', 0);
    });

    // Check completion
    this.checkEquationBuilderCompletion();

    console.log('Equation builder cleared');
  }

  /**
   * Check equation builder completion
   */
  checkEquationBuilderCompletion() {
    const hasTerms = this.equationBuilderState.currentEquation.length > 0;

    // Enable next button if equation has any terms
    document.getElementById('next-question-btn').disabled = !hasTerms;

    // Check if equation is correct
    const isCorrect = this.checkEquationCorrectness();

    // Provide visual feedback
    const equationArea = document.getElementById('equation-area');
    if (equationArea) {
      equationArea.style.borderColor = isCorrect ? '#28a745' : '#1547bb';
      equationArea.style.backgroundColor = isCorrect ? '#f8fff9' : '#fafbff';
    }

    console.log('Equation completion check:', { hasTerms, isCorrect });
  }

  /**
   * Check if current equation matches the correct equation
   */
  checkEquationCorrectness() {
    const { currentEquation, correctEquation } = this.equationBuilderState;

    if (currentEquation.length !== correctEquation.length) {
      return false;
    }

    // Check exact match
    for (let i = 0; i < currentEquation.length; i++) {
      if (currentEquation[i] !== correctEquation[i]) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get equation builder answer
   */
  getEquationBuilderAnswer() {
    return JSON.stringify({
      currentEquation: this.equationBuilderState.currentEquation,
      equationString: this.equationBuilderState.currentEquation.join(' ')
    });
  }

  /**
   * Show pattern completion interactive question
   */
  showPatternCompletion(question) {
    const container = document.getElementById('pattern-completion');
    const canvas = document.getElementById('pattern-canvas');
    const optionsContainer = document.getElementById('pattern-options');
    const answerSlot = document.getElementById('pattern-answer-slot');

    // Get configuration from question
    const config = question.config || {};
    const {
      patternType = 'arithmetic',
      sequence = [],
      options = [],
      correctAnswer = '',
      rule = '',
      hint = ''
    } = config;

    // Set up canvas
    canvas.width = 600;
    canvas.height = 150;
    const ctx = canvas.getContext('2d');

    // Initialize pattern completion state
    this.patternCompletionState = {
      canvas: canvas,
      ctx: ctx,
      patternType: patternType,
      sequence: sequence,
      options: options,
      correctAnswer: correctAnswer,
      rule: rule,
      hint: hint,
      selectedAnswer: null,
      isComplete: false
    };

    // Clear containers
    optionsContainer.innerHTML = '';
    answerSlot.innerHTML = '<span class="slot-placeholder">?</span>';
    answerSlot.classList.remove('filled');

    // Draw pattern sequence
    this.drawPatternSequence();

    // Create option buttons
    this.createPatternOptions();

    // Set up controls
    this.setupPatternCompletionControls();

    // Show the container
    container.classList.remove('hidden');

    console.log('Pattern completion initialized:', { patternType, sequence: sequence.length, options: options.length });
  }

  /**
   * Draw pattern sequence on canvas
   */
  drawPatternSequence() {
    const { ctx, canvas, sequence, patternType } = this.patternCompletionState;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set up drawing styles
    ctx.strokeStyle = '#1547bb';
    ctx.fillStyle = '#1547bb';
    ctx.lineWidth = 2;
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';

    // Calculate spacing
    const totalItems = sequence.length + 1; // +1 for the question mark
    const spacing = canvas.width / (totalItems + 1);
    const centerY = canvas.height / 2;

    // Draw sequence items
    sequence.forEach((item, index) => {
      const x = spacing * (index + 1);

      if (patternType === 'visual') {
        this.drawVisualPatternItem(ctx, item, x, centerY);
      } else {
        this.drawTextPatternItem(ctx, item, x, centerY);
      }

      // Draw arrow to next item (except for last item)
      if (index < sequence.length - 1) {
        this.drawArrow(ctx, x + 40, centerY, x + spacing - 40, centerY);
      }
    });

    // Draw question mark for the next item
    const questionX = spacing * (sequence.length + 1);
    ctx.fillStyle = '#999';
    ctx.font = 'bold 32px Arial';
    ctx.fillText('?', questionX, centerY + 10);

    // Draw final arrow
    if (sequence.length > 0) {
      const lastX = spacing * sequence.length;
      this.drawArrow(ctx, lastX + 40, centerY, questionX - 40, centerY);
    }
  }

  /**
   * Draw visual pattern item (shapes)
   */
  drawVisualPatternItem(ctx, item, x, y) {
    const size = 30;

    ctx.fillStyle = '#1547bb';
    ctx.strokeStyle = '#1547bb';

    switch (item) {
      case 'circle':
        ctx.beginPath();
        ctx.arc(x, y, size/2, 0, 2 * Math.PI);
        ctx.fill();
        break;
      case 'triangle':
        ctx.beginPath();
        ctx.moveTo(x, y - size/2);
        ctx.lineTo(x - size/2, y + size/2);
        ctx.lineTo(x + size/2, y + size/2);
        ctx.closePath();
        ctx.fill();
        break;
      case 'square':
        ctx.fillRect(x - size/2, y - size/2, size, size);
        break;
      case 'star':
        this.drawStar(ctx, x, y, size/2);
        break;
      default:
        // Default to circle
        ctx.beginPath();
        ctx.arc(x, y, size/2, 0, 2 * Math.PI);
        ctx.fill();
    }
  }

  /**
   * Draw text pattern item (numbers, expressions)
   */
  drawTextPatternItem(ctx, item, x, y) {
    // Draw background circle
    ctx.fillStyle = '#f0f4ff';
    ctx.beginPath();
    ctx.arc(x, y, 35, 0, 2 * Math.PI);
    ctx.fill();

    // Draw border
    ctx.strokeStyle = '#1547bb';
    ctx.beginPath();
    ctx.arc(x, y, 35, 0, 2 * Math.PI);
    ctx.stroke();

    // Draw text
    ctx.fillStyle = '#121c41';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(item.toString(), x, y + 5);
  }

  /**
   * Draw arrow between pattern items
   */
  drawArrow(ctx, fromX, fromY, toX, toY) {
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 2;

    // Draw line
    ctx.beginPath();
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(toX, toY);
    ctx.stroke();

    // Draw arrowhead
    const headLength = 10;
    const angle = Math.atan2(toY - fromY, toX - fromX);

    ctx.beginPath();
    ctx.moveTo(toX, toY);
    ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI/6), toY - headLength * Math.sin(angle - Math.PI/6));
    ctx.moveTo(toX, toY);
    ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI/6), toY - headLength * Math.sin(angle + Math.PI/6));
    ctx.stroke();
  }

  /**
   * Draw star shape
   */
  drawStar(ctx, x, y, radius) {
    const spikes = 5;
    const outerRadius = radius;
    const innerRadius = radius * 0.4;

    ctx.beginPath();
    for (let i = 0; i < spikes * 2; i++) {
      const angle = (i * Math.PI) / spikes;
      const r = i % 2 === 0 ? outerRadius : innerRadius;
      const pointX = x + r * Math.cos(angle - Math.PI/2);
      const pointY = y + r * Math.sin(angle - Math.PI/2);

      if (i === 0) {
        ctx.moveTo(pointX, pointY);
      } else {
        ctx.lineTo(pointX, pointY);
      }
    }
    ctx.closePath();
    ctx.fill();
  }

  /**
   * Create pattern option buttons
   */
  createPatternOptions() {
    const optionsContainer = document.getElementById('pattern-options');
    const { options, patternType } = this.patternCompletionState;

    options.forEach((option, index) => {
      const optionButton = document.createElement('div');
      optionButton.className = 'pattern-option';
      optionButton.dataset.optionValue = option;
      optionButton.dataset.optionIndex = index;

      if (patternType === 'visual') {
        // For visual patterns, create a mini canvas
        const miniCanvas = document.createElement('canvas');
        miniCanvas.width = 40;
        miniCanvas.height = 40;
        const miniCtx = miniCanvas.getContext('2d');
        this.drawVisualPatternItem(miniCtx, option, 20, 20);
        optionButton.appendChild(miniCanvas);
      } else {
        // For text patterns, just show the text
        optionButton.textContent = option;
      }

      // Add click handler
      optionButton.addEventListener('click', () => {
        this.selectPatternOption(option, optionButton);
      });

      optionsContainer.appendChild(optionButton);
    });
  }

  /**
   * Handle pattern option selection
   */
  selectPatternOption(selectedValue, optionElement) {
    // Clear previous selection
    document.querySelectorAll('.pattern-option').forEach(option => {
      option.classList.remove('selected');
    });

    // Select this option
    optionElement.classList.add('selected');
    this.patternCompletionState.selectedAnswer = selectedValue;

    // Update answer slot
    const answerSlot = document.getElementById('pattern-answer-slot');
    answerSlot.classList.add('filled');

    if (this.patternCompletionState.patternType === 'visual') {
      // Create mini canvas for visual answer
      answerSlot.innerHTML = '';
      const miniCanvas = document.createElement('canvas');
      miniCanvas.width = 40;
      miniCanvas.height = 40;
      const miniCtx = miniCanvas.getContext('2d');
      this.drawVisualPatternItem(miniCtx, selectedValue, 20, 20);
      answerSlot.appendChild(miniCanvas);
    } else {
      answerSlot.textContent = selectedValue;
    }

    // Check completion
    this.checkPatternCompletionCompletion();

    console.log('Pattern option selected:', selectedValue);
  }

  /**
   * Set up pattern completion controls
   */
  setupPatternCompletionControls() {
    const clearBtn = document.getElementById('clear-pattern-btn');
    const hintBtn = document.getElementById('hint-pattern-btn');

    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearPatternCompletion();
      });
    }

    if (hintBtn) {
      hintBtn.addEventListener('click', () => {
        this.showPatternHint();
      });
    }

    // Store cleanup function
    this.patternCompletionCleanup = () => {
      if (clearBtn) clearBtn.removeEventListener('click', this.clearPatternCompletion);
      if (hintBtn) hintBtn.removeEventListener('click', this.showPatternHint);

      // Remove option click handlers
      document.querySelectorAll('.pattern-option').forEach(option => {
        option.removeEventListener('click', this.selectPatternOption);
      });
    };
  }

  /**
   * Clear pattern completion selection
   */
  clearPatternCompletion() {
    // Clear selection
    document.querySelectorAll('.pattern-option').forEach(option => {
      option.classList.remove('selected');
    });

    // Reset answer slot
    const answerSlot = document.getElementById('pattern-answer-slot');
    answerSlot.classList.remove('filled');
    answerSlot.innerHTML = '<span class="slot-placeholder">?</span>';

    // Reset state
    this.patternCompletionState.selectedAnswer = null;
    this.patternCompletionState.isComplete = false;

    // Disable next button
    document.getElementById('next-question-btn').disabled = true;

    console.log('Pattern completion cleared');
  }

  /**
   * Show pattern hint
   */
  showPatternHint() {
    const { hint, rule } = this.patternCompletionState;
    const hintText = hint || rule || 'Look for the pattern in the sequence';

    // Create or update hint display
    let hintDisplay = document.getElementById('pattern-hint-display');
    if (!hintDisplay) {
      hintDisplay = document.createElement('div');
      hintDisplay.id = 'pattern-hint-display';
      hintDisplay.className = 'pattern-hint-display';
      hintDisplay.style.cssText = `
        background: #fff3cd;
        color: #856404;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #ffeaa7;
        margin-top: 15px;
        font-style: italic;
        text-align: center;
      `;

      const container = document.getElementById('pattern-completion');
      container.appendChild(hintDisplay);
    }

    hintDisplay.textContent = `💡 Hint: ${hintText}`;
    hintDisplay.style.display = 'block';

    console.log('Pattern hint shown:', hintText);
  }

  /**
   * Check pattern completion
   */
  checkPatternCompletionCompletion() {
    const hasSelection = this.patternCompletionState.selectedAnswer !== null;

    // Enable next button if selection is made
    document.getElementById('next-question-btn').disabled = !hasSelection;

    if (hasSelection) {
      this.patternCompletionState.isComplete = true;
      this.showCompletionIndicator();
      this.clearFallbackTimer();
    }

    console.log('Pattern completion check:', { hasSelection, selectedAnswer: this.patternCompletionState.selectedAnswer });
  }

  /**
   * Get pattern completion answer
   */
  getPatternCompletionAnswer() {
    if (!this.patternCompletionState) {
      return JSON.stringify({
        type: 'pattern-completion',
        selectedAnswer: null,
        isComplete: false,
        error: 'No pattern completion state found'
      });
    }

    const { selectedAnswer, correctAnswer, sequence, patternType, rule } = this.patternCompletionState;

    // Get performance data for this question
    const performanceData = this.currentQuestionPerformance || {};
    const questionIndex = this.currentQuestionIndex;
    const interactionLogs = this.interactionLogs[questionIndex] || [];

    return JSON.stringify({
      type: 'pattern-completion',
      selectedAnswer: selectedAnswer,
      correctAnswer: correctAnswer,
      sequence: sequence,
      patternType: patternType,
      rule: rule,
      isCorrect: selectedAnswer === correctAnswer,
      isComplete: this.patternCompletionState.isComplete,
      timestamp: new Date().toISOString(),

      // Enhanced performance data
      performanceMetrics: {
        timeSpent: performanceData.completionTime ?
          performanceData.completionTime - performanceData.startTime : null,
        firstInteractionTime: performanceData.firstInteractionTime ?
          performanceData.firstInteractionTime - performanceData.startTime : null,
        totalInteractions: performanceData.difficultyIndicators?.totalInteractions || 0,
        attempts: performanceData.attempts || 0,
        hintsUsed: performanceData.hintsUsed || 0,
        errorsCount: performanceData.errorsCount || 0,
        completionEfficiency: performanceData.difficultyIndicators?.completionEfficiency || 0,
        patternRecognitionSpeed: performanceData.firstInteractionTime ?
          performanceData.firstInteractionTime - performanceData.startTime : null
      },

      // Pattern-specific metrics
      patternMetrics: {
        patternComplexity: this.assessPatternComplexity(patternType, sequence),
        recognitionStrategy: this.analyzePatternRecognitionStrategy(interactionLogs),
        sequenceLength: sequence ? sequence.length : 0,
        patternCategory: patternType,
        mathematicalConcept: this.identifyMathematicalConcept(patternType, rule)
      },

      // Detailed interaction logs
      interactionSequence: interactionLogs.map(log => ({
        type: log.type,
        timestamp: log.timestamp,
        timeFromStart: log.timeFromStart,
        details: log.details
      })),

      // Solution path analysis
      solutionPath: performanceData.solutionPath || [],

      // Cognitive processing indicators
      cognitiveMetrics: {
        patternRecognitionAbility: selectedAnswer === correctAnswer && performanceData.hintsUsed === 0,
        abstractReasoning: patternType === 'algebraic' && selectedAnswer === correctAnswer,
        visualProcessing: patternType === 'visual' && performanceData.firstInteractionTime < 10000,
        logicalSequencing: performanceData.errorsCount === 0 && this.patternCompletionState.isComplete
      }
    });
  }

  /**
   * Assess pattern complexity for analysis
   */
  assessPatternComplexity(patternType, sequence) {
    if (!sequence || sequence.length === 0) return 'unknown';

    switch (patternType) {
      case 'arithmetic':
        return sequence.length <= 4 ? 'simple' : 'moderate';
      case 'geometric':
        return 'moderate';
      case 'visual':
        return sequence.length <= 5 ? 'simple' : 'complex';
      case 'algebraic':
        return 'complex';
      case 'quadratic':
        return 'complex';
      default:
        return 'moderate';
    }
  }

  /**
   * Analyze pattern recognition strategy from interactions
   */
  analyzePatternRecognitionStrategy(interactionLogs) {
    if (!interactionLogs || interactionLogs.length === 0) return 'no_interaction';

    const hintRequests = interactionLogs.filter(log => log.type === 'hint_request').length;
    const attempts = interactionLogs.filter(log => log.type === 'attempt').length;
    const errors = interactionLogs.filter(log => log.type === 'error').length;

    if (hintRequests === 0 && attempts === 1 && errors === 0) {
      return 'immediate_recognition';
    } else if (hintRequests === 0 && attempts <= 2 && errors <= 1) {
      return 'quick_analysis';
    } else if (hintRequests > 0 && attempts <= 3) {
      return 'guided_discovery';
    } else if (attempts > 3 || errors > 2) {
      return 'trial_and_error';
    } else {
      return 'systematic_analysis';
    }
  }

  /**
   * Identify mathematical concept from pattern type and rule
   */
  identifyMathematicalConcept(patternType, rule) {
    if (!rule) return 'unknown';

    const ruleLower = rule.toLowerCase();

    if (ruleLower.includes('add') || ruleLower.includes('+')) {
      return 'arithmetic_progression';
    } else if (ruleLower.includes('multiply') || ruleLower.includes('×') || ruleLower.includes('double')) {
      return 'geometric_progression';
    } else if (ruleLower.includes('square') || ruleLower.includes('²')) {
      return 'quadratic_sequence';
    } else if (ruleLower.includes('repeat') || ruleLower.includes('pattern')) {
      return 'repeating_pattern';
    } else if (patternType === 'algebraic') {
      return 'algebraic_sequence';
    } else {
      return 'general_pattern';
    }
  }

  // REMOVED: Old area model implementation methods

  /**
   * Handle option selection for multiple choice
   */
  selectOption(e) {
    const selectedBtn = e.target;
    const container = document.getElementById('multiple-choice-options');
    const buttons = container.querySelectorAll('.option-btn');
    
    // Remove selection from all buttons
    buttons.forEach(btn => btn.classList.remove('selected'));
    
    // Add selection to clicked button
    selectedBtn.classList.add('selected');
    
    // Enable next button
    document.getElementById('next-question-btn').disabled = false;
  }

  /**
   * Handle numeric input
   */
  handleNumericInput() {
    const input = document.getElementById('numeric-answer');
    const value = input.value.trim();
    
    // Enable next button if there's a value
    document.getElementById('next-question-btn').disabled = value === '';
  }

  /**
   * Handle short answer input
   */
  handleShortAnswerInput() {
    const input = document.getElementById('short-answer');
    const value = input.value.trim();
    
    // Enable next button if there's a value
    document.getElementById('next-question-btn').disabled = value === '';
  }

  /**
   * Move to next question
   */
  nextQuestion() {
    this.saveCurrentAnswer();
    this.cleanupInteractiveQuestions();
    this.currentQuestionIndex++;
    this.loadCurrentQuestion();
  }

  /**
   * Skip current question
   */
  skipQuestion() {
    this.saveCurrentAnswer(''); // Save empty answer
    this.cleanupInteractiveQuestions();
    this.currentQuestionIndex++;
    this.loadCurrentQuestion();
  }

  /**
   * Clean up interactive question event listeners
   */
  cleanupInteractiveQuestions() {
    // Clean up number line
    if (this.numberLineCleanup) {
      this.numberLineCleanup();
      this.numberLineCleanup = null;
    }

    // Clean up drag and drop
    if (this.dragDropCleanup) {
      this.dragDropCleanup();
      this.dragDropCleanup = null;
    }

    // Clean up visual calculator
    if (this.visualCalculatorCleanup) {
      this.visualCalculatorCleanup();
      this.visualCalculatorCleanup = null;
    }

    // Clean up number bonds
    if (this.numberBondsCleanup) {
      this.numberBondsCleanup();
      this.numberBondsCleanup = null;
    }

    // Clean up step-by-step
    if (this.stepByStepCleanup) {
      this.stepByStepCleanup();
      this.stepByStepCleanup = null;
    }

    // Clean up coordinate plotting
    if (this.coordinatePlottingCleanup) {
      this.coordinatePlottingCleanup();
      this.coordinatePlottingCleanup = null;
    }

    // Clean up ratio sliders
    if (this.ratioSlidersCleanup) {
      this.ratioSlidersCleanup();
      this.ratioSlidersCleanup = null;
    }

    // Clean up equation builder
    if (this.equationBuilderCleanup) {
      this.equationBuilderCleanup();
      this.equationBuilderCleanup = null;
    }

    // Clean up pattern completion
    if (this.patternCompletionCleanup) {
      this.patternCompletionCleanup();
      this.patternCompletionCleanup = null;
    }

    // Reset state variables
    this.currentNumberLineValue = null;
    this.numberLineConfig = null;
    this.dragDropState = null;
    this.visualCalculatorState = null;
    this.numberBondsState = null;
    this.stepByStepState = null;
    this.coordinatePlottingState = null;
    this.ratioSlidersState = null;
    this.equationBuilderState = null;
    this.patternCompletionState = null;

    console.log('All interactive questions cleaned up');
  }

  /**
   * Save the current answer
   */
  saveCurrentAnswer(forcedAnswer = null) {
    const question = this.questions[this.currentQuestionIndex];
    let answer = forcedAnswer;
    
    if (answer === null) {
      if (question.type === 'multiple-choice') {
        const selectedBtn = document.querySelector('.option-btn.selected');
        answer = selectedBtn ? selectedBtn.textContent : '';
      } else if (question.type === 'numeric') {
        answer = document.getElementById('numeric-answer').value.trim();
      } else if (question.type === 'short-answer') {
        answer = document.getElementById('short-answer').value.trim();
      } else if (question.type === 'number-line') {
        answer = this.currentNumberLineValue?.toString() || '';
      } else if (question.type === 'drag-drop') {
        answer = this.getDragDropAnswer();
      } else if (question.type === 'visual-calculator') {
        answer = this.getVisualCalculatorAnswer();
      } else if (question.type === 'number-bonds') {
        answer = this.getNumberBondsAnswer();
      } else if (question.type === 'step-by-step') {
        answer = this.getStepByStepAnswer();
      } else if (question.type === 'coordinate-plot') {
        answer = this.getCoordinatePlottingAnswer();
      } else if (question.type === 'ratio-slider') {
        answer = this.getRatioSlidersAnswer();
      } else if (question.type === 'equation-builder') {
        answer = this.getEquationBuilderAnswer();
      } else if (question.type === 'pattern-completion') {
        answer = this.getPatternCompletionAnswer();
      }
    }
    
    // Finalize performance tracking if not already done
    if (this.currentQuestionPerformance && !this.currentQuestionPerformance.isComplete) {
      this.finalizeQuestionPerformance(true);
    }

    this.answers[this.currentQuestionIndex] = {
      questionId: question.id,
      questionType: question.type,
      topic: question.topic,
      studentAnswer: answer,
      timeSpent: new Date() - this.questionStartTimes[this.currentQuestionIndex],

      // Enhanced performance data for AI analysis
      performanceMetrics: this.currentQuestionPerformance ? {
        totalTimeSpent: this.currentQuestionPerformance.completionTime ?
          this.currentQuestionPerformance.completionTime - this.currentQuestionPerformance.startTime : null,
        firstInteractionDelay: this.currentQuestionPerformance.difficultyIndicators?.timeToFirstInteraction || null,
        totalInteractions: this.currentQuestionPerformance.difficultyIndicators?.totalInteractions || 0,
        attempts: this.currentQuestionPerformance.attempts || 0,
        hintsUsed: this.currentQuestionPerformance.hintsUsed || 0,
        errorsCount: this.currentQuestionPerformance.errorsCount || 0,
        completionEfficiency: this.currentQuestionPerformance.difficultyIndicators?.completionEfficiency || 0,
        errorRate: this.currentQuestionPerformance.difficultyIndicators?.errorRate || 0,
        hintDependency: this.currentQuestionPerformance.difficultyIndicators?.hintDependency || false,
        solutionPath: this.currentQuestionPerformance.solutionPath || [],
        isComplete: this.currentQuestionPerformance.isComplete || false
      } : null,

      // Detailed interaction sequence for AI analysis
      interactionSequence: this.interactionLogs[this.currentQuestionIndex] || [],

      // Question-specific analysis data
      questionAnalysis: this.generateQuestionAnalysis(question, answer)
    };
  }

  /**
   * Generate question-specific analysis data for AI processing
   */
  generateQuestionAnalysis(question, answer) {
    const analysis = {
      questionType: question.type,
      topic: question.topic,
      difficulty: question.difficulty || 'medium',
      cognitiveLoad: this.assessCognitiveLoad(question),
      problemSolvingApproach: this.identifyProblemSolvingApproach(question.type, answer),
      mathematicalConcepts: this.extractMathematicalConcepts(question),
      answerQuality: this.assessAnswerQuality(question, answer)
    };

    return analysis;
  }

  /**
   * Assess cognitive load of a question
   */
  assessCognitiveLoad(question) {
    const complexityFactors = {
      'multiple-choice': 1,
      'numeric': 2,
      'short-answer': 2,
      'number-line': 3,
      'drag-drop': 3,
      'visual-calculator': 4,
      'number-bonds': 3,
      'step-by-step': 5,
      'coordinate-plot': 4,
      'ratio-slider': 3,
      'equation-builder': 5,
      'pattern-completion': 4
    };

    const baseComplexity = complexityFactors[question.type] || 3;
    const topicComplexity = question.topic?.includes('algebra') ? 1.5 : 1;

    return Math.min(5, Math.round(baseComplexity * topicComplexity));
  }

  /**
   * Identify problem-solving approach from question type and answer
   */
  identifyProblemSolvingApproach(questionType, answer) {
    try {
      const parsedAnswer = typeof answer === 'string' ? JSON.parse(answer) : answer;

      switch (questionType) {
        case 'drag-drop':
          return parsedAnswer.performanceMetrics?.completionEfficiency > 0.8 ? 'systematic' : 'exploratory';
        case 'visual-calculator':
          return parsedAnswer.calculatorMetrics?.stepsToCompletion <= 3 ? 'efficient' : 'methodical';
        case 'number-bonds':
          return parsedAnswer.reasoningMetrics?.strategicApproach ? 'strategic' : 'computational';
        case 'step-by-step':
          return parsedAnswer.stepByStepMetrics?.sequentialProgress ? 'systematic' : 'fragmented';
        case 'pattern-completion':
          return parsedAnswer.patternMetrics?.recognitionStrategy || 'analytical';
        default:
          return 'standard';
      }
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Extract mathematical concepts from question
   */
  extractMathematicalConcepts(question) {
    const concepts = [];

    if (question.topic) {
      concepts.push(question.topic);
    }

    if (question.type === 'number-bonds') {
      concepts.push('number_relationships', 'arithmetic_operations');
    } else if (question.type === 'visual-calculator') {
      concepts.push('calculation_skills', 'operation_sequencing');
    } else if (question.type === 'coordinate-plot') {
      concepts.push('coordinate_geometry', 'spatial_reasoning');
    } else if (question.type === 'pattern-completion') {
      concepts.push('pattern_recognition', 'sequence_analysis');
    } else if (question.type === 'ratio-slider') {
      concepts.push('proportional_reasoning', 'ratio_concepts');
    }

    return concepts;
  }

  /**
   * Assess quality of student answer
   */
  assessAnswerQuality(question, answer) {
    try {
      if (typeof answer === 'string' && answer.startsWith('{')) {
        const parsedAnswer = JSON.parse(answer);

        return {
          completeness: parsedAnswer.isComplete || parsedAnswer.completed || false,
          accuracy: parsedAnswer.isCorrect || false,
          efficiency: parsedAnswer.performanceMetrics?.completionEfficiency || 0,
          independence: (parsedAnswer.performanceMetrics?.hintsUsed || 0) === 0,
          persistence: (parsedAnswer.performanceMetrics?.attempts || 1) <= 3
        };
      } else {
        // Simple answer quality assessment
        return {
          completeness: answer && answer.toString().trim().length > 0,
          accuracy: null, // Cannot determine without correct answer
          efficiency: null,
          independence: null,
          persistence: null
        };
      }
    } catch (error) {
      return {
        completeness: false,
        accuracy: null,
        efficiency: 0,
        independence: null,
        persistence: null
      };
    }
  }

  /**
   * Complete the assessment
   */
  async completeAssessment() {
    try {
      // Show loading
      this.showLoading('Processing your assessment...');
      
      // Stop timer
      this.stopTimer();
      
      // Submit assessment
      const results = await this.submitAssessment();
      
      // Hide loading and show results
      this.hideLoading();
      this.showResults(results);
      
    } catch (error) {
      console.error('Error completing assessment:', error);
      this.hideLoading();
      alert('Failed to complete assessment. Please try again.');
    }
  }

  /**
   * Submit assessment for grading
   */
  async submitAssessment() {
    try {
      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3003'
        : window.location.origin;

      const timeSpent = this.timeLimit - this.timeRemaining;

      // Prepare comprehensive assessment data for AI analysis
      const assessmentData = {
        answers: this.answers,
        email: this.userData.email,
        level: this.currentLevel,
        timeSpent: timeSpent,
        userData: {
          firstName: this.userData.firstName,
          lastName: this.userData.lastName,
          name: this.userData.name,
          studentLevel: this.userData.studentLevel,
          userType: 'student' // Set as student for math-only users
        },

        // Enhanced performance analytics for AI analysis
        performanceAnalytics: {
          totalAssessmentTime: timeSpent,
          questionsAttempted: this.answers.length,
          interactiveQuestionsCount: this.answers.filter(a =>
            ['drag-drop', 'visual-calculator', 'number-bonds', 'step-by-step',
             'coordinate-plot', 'ratio-slider', 'equation-builder', 'pattern-completion'].includes(a.questionType)
          ).length,

          // Aggregate performance metrics
          aggregateMetrics: this.calculateAggregatePerformanceMetrics(),

          // Question-by-question performance data
          detailedPerformance: this.answers.map(answer => ({
            questionId: answer.questionId,
            questionType: answer.questionType,
            topic: answer.topic,
            timeSpent: answer.timeSpent,
            performanceMetrics: answer.performanceMetrics,
            interactionCount: answer.interactionSequence?.length || 0,
            completionStatus: answer.performanceMetrics?.isComplete || false,
            difficultyIndicators: answer.performanceMetrics ? {
              hintsNeeded: answer.performanceMetrics.hintsUsed > 0,
              multipleAttempts: answer.performanceMetrics.attempts > 1,
              errorProne: answer.performanceMetrics.errorsCount > 0,
              timeIntensive: answer.performanceMetrics.totalTimeSpent > 120000, // 2 minutes
              lowEfficiency: answer.performanceMetrics.completionEfficiency < 0.5
            } : null
          })),

          // Learning pattern analysis
          learningPatterns: this.analyzeLearningPatterns()
        }
      };

      const response = await fetch(`${baseUrl}/api/math-assessments/${this.assessmentId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assessmentData),
      });

      if (!response.ok) {
        throw new Error(`Failed to submit assessment: ${response.status}`);
      }

      const results = await response.json();
      
      console.log('Assessment submitted successfully:', results);
      return results;

    } catch (error) {
      console.error('Error submitting assessment:', error);
      throw error;
    }
  }

  /**
   * Calculate aggregate performance metrics across all questions
   */
  calculateAggregatePerformanceMetrics() {
    const interactiveAnswers = this.answers.filter(a => a.performanceMetrics);

    if (interactiveAnswers.length === 0) {
      return {
        averageTimePerQuestion: 0,
        averageInteractions: 0,
        averageAttempts: 0,
        totalHintsUsed: 0,
        totalErrors: 0,
        averageEfficiency: 0,
        completionRate: 0,
        independenceScore: 0,
        persistenceScore: 0
      };
    }

    const totals = interactiveAnswers.reduce((acc, answer) => {
      const metrics = answer.performanceMetrics;
      return {
        totalTime: acc.totalTime + (metrics.totalTimeSpent || 0),
        totalInteractions: acc.totalInteractions + (metrics.totalInteractions || 0),
        totalAttempts: acc.totalAttempts + (metrics.attempts || 0),
        totalHints: acc.totalHints + (metrics.hintsUsed || 0),
        totalErrors: acc.totalErrors + (metrics.errorsCount || 0),
        totalEfficiency: acc.totalEfficiency + (metrics.completionEfficiency || 0),
        completedQuestions: acc.completedQuestions + (metrics.isComplete ? 1 : 0),
        independentSolutions: acc.independentSolutions + (metrics.hintsUsed === 0 ? 1 : 0),
        quickSolutions: acc.quickSolutions + (metrics.attempts <= 2 ? 1 : 0)
      };
    }, {
      totalTime: 0, totalInteractions: 0, totalAttempts: 0, totalHints: 0,
      totalErrors: 0, totalEfficiency: 0, completedQuestions: 0,
      independentSolutions: 0, quickSolutions: 0
    });

    const count = interactiveAnswers.length;

    return {
      averageTimePerQuestion: totals.totalTime / count,
      averageInteractions: totals.totalInteractions / count,
      averageAttempts: totals.totalAttempts / count,
      totalHintsUsed: totals.totalHints,
      totalErrors: totals.totalErrors,
      averageEfficiency: totals.totalEfficiency / count,
      completionRate: totals.completedQuestions / count,
      independenceScore: totals.independentSolutions / count,
      persistenceScore: totals.quickSolutions / count,

      // Additional derived metrics
      errorRate: totals.totalErrors / Math.max(totals.totalAttempts, 1),
      hintDependencyRate: totals.totalHints / count,
      engagementLevel: Math.min(totals.totalInteractions / count / 10, 1) // Normalized to 0-1
    };
  }

  /**
   * Analyze learning patterns from performance data
   */
  analyzeLearningPatterns() {
    const interactiveAnswers = this.answers.filter(a => a.performanceMetrics);

    if (interactiveAnswers.length === 0) {
      return {
        learningTrajectory: 'insufficient_data',
        strengthAreas: [],
        challengeAreas: [],
        problemSolvingStyle: 'unknown',
        adaptabilityScore: 0,
        metacognitionLevel: 'unknown'
      };
    }

    // Analyze performance trends over time
    const performanceOverTime = interactiveAnswers.map((answer, index) => ({
      questionIndex: index,
      efficiency: answer.performanceMetrics.completionEfficiency || 0,
      timeSpent: answer.performanceMetrics.totalTimeSpent || 0,
      attempts: answer.performanceMetrics.attempts || 0,
      hintsUsed: answer.performanceMetrics.hintsUsed || 0
    }));

    // Identify learning trajectory
    const efficiencyTrend = this.calculateTrend(performanceOverTime.map(p => p.efficiency));
    const timeTrend = this.calculateTrend(performanceOverTime.map(p => p.timeSpent));

    let learningTrajectory = 'stable';
    if (efficiencyTrend > 0.1) learningTrajectory = 'improving';
    else if (efficiencyTrend < -0.1) learningTrajectory = 'declining';

    // Identify strength and challenge areas by topic
    const topicPerformance = {};
    interactiveAnswers.forEach(answer => {
      const topic = answer.topic || 'general';
      if (!topicPerformance[topic]) {
        topicPerformance[topic] = { scores: [], count: 0 };
      }
      topicPerformance[topic].scores.push(answer.performanceMetrics.completionEfficiency || 0);
      topicPerformance[topic].count++;
    });

    const strengthAreas = [];
    const challengeAreas = [];

    Object.entries(topicPerformance).forEach(([topic, data]) => {
      const avgScore = data.scores.reduce((a, b) => a + b, 0) / data.scores.length;
      if (avgScore > 0.7) strengthAreas.push(topic);
      else if (avgScore < 0.4) challengeAreas.push(topic);
    });

    // Determine problem-solving style
    const avgHints = interactiveAnswers.reduce((sum, a) => sum + (a.performanceMetrics.hintsUsed || 0), 0) / interactiveAnswers.length;
    const avgAttempts = interactiveAnswers.reduce((sum, a) => sum + (a.performanceMetrics.attempts || 0), 0) / interactiveAnswers.length;

    let problemSolvingStyle = 'balanced';
    if (avgHints > 1) problemSolvingStyle = 'guided_learner';
    else if (avgAttempts > 3) problemSolvingStyle = 'trial_and_error';
    else if (avgHints === 0 && avgAttempts <= 2) problemSolvingStyle = 'confident_solver';

    return {
      learningTrajectory,
      strengthAreas,
      challengeAreas,
      problemSolvingStyle,
      adaptabilityScore: Math.max(0, 1 - Math.abs(efficiencyTrend)), // Higher for stable performance
      metacognitionLevel: avgHints > 0 && avgHints < 2 ? 'good' : avgHints === 0 ? 'high' : 'developing',
      performanceTrends: {
        efficiencyTrend,
        timeTrend,
        consistencyScore: 1 - this.calculateVariance(performanceOverTime.map(p => p.efficiency))
      }
    };
  }

  /**
   * Calculate trend (slope) of a data series
   */
  calculateTrend(data) {
    if (data.length < 2) return 0;

    const n = data.length;
    const sumX = (n * (n - 1)) / 2; // Sum of indices 0, 1, 2, ...
    const sumY = data.reduce((a, b) => a + b, 0);
    const sumXY = data.reduce((sum, y, x) => sum + x * y, 0);
    const sumXX = (n * (n - 1) * (2 * n - 1)) / 6; // Sum of squares of indices

    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  /**
   * Calculate variance of a data series
   */
  calculateVariance(data) {
    if (data.length === 0) return 0;

    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    const variance = data.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / data.length;

    return Math.sqrt(variance); // Return standard deviation
  }

  /**
   * Show assessment results
   */
  showResults(results) {
    try {
      // Store results for detailed modal
      this.assessmentResults = results;

      // Hide all other screens first
      this.hideAllAssessmentScreens();

      // Hide calculator widget when showing results
      this.hideCalculatorWidget();

      // Show only results container
      const resultsContainer = document.getElementById('assessment-results');
      if (!resultsContainer) {
        console.error('Assessment results container not found');
        return;
      }
      resultsContainer.classList.remove('hidden');

    // Ensure results container is scrollable and focused for accessibility
    const resultsContentContainer = document.querySelector('.results-container');
    if (resultsContentContainer) {
      resultsContentContainer.setAttribute('tabindex', '0');
      setTimeout(() => {
        resultsContentContainer.focus();
        resultsContentContainer.scrollTop = 0; // Scroll to top of results
      }, 100);
    }

    // Update results display
    document.getElementById('final-score').textContent = results.score;
    document.getElementById('max-score').textContent = results.maxScore;

    // Update status with encouraging, level-based messaging
    const statusElement = document.getElementById('status-text');
    const statusBadge = statusElement.parentElement;

    // Clear previous status classes
    statusBadge.classList.remove('passed', 'not-passed', 'current-level', 'progress-made');

    if (results.passed) {
      statusElement.textContent = `${this.currentLevel} Level Complete!`;
      statusBadge.classList.add('passed');

      // Check if user can progress to next level
      this.checkLevelProgression(results);
    } else {
      // Show current achievement level instead of "Not Passed"
      const achievementLevel = this.determineAchievementLevel(results.score, results.maxScore);
      statusElement.textContent = `${achievementLevel.level} Achievement`;
      statusBadge.classList.add(achievementLevel.class);

      // Add encouraging subtitle
      this.addEncouragingMessage(achievementLevel, results);
    }

    // Show topic breakdown if available
    if (results.topicBreakdown) {
      this.displayTopicBreakdown(results.topicBreakdown);
    }

    // Show detailed feedback if available
    if (results.feedback) {
      this.displayFeedback(results.feedback);
    }

    // Show strengths and improvements
    if (results.strengths || results.improvements) {
      this.displayStrengthsAndImprovements(results.strengths, results.improvements);
    }

    // Show recommendations if available
    if (results.placementRecommendation) {
      this.displayRecommendations(results.placementRecommendation);
    }

    // Add progression options if applicable
    this.addProgressionOptions(results);

    } catch (error) {
      console.error('Error displaying assessment results:', error);
      // Show a fallback message to the user
      const fallbackContainer = document.getElementById('assessment-results');
      if (fallbackContainer) {
        fallbackContainer.innerHTML = `
          <div class="error-message">
            <h2>Assessment Complete</h2>
            <p>Your assessment has been completed successfully, but there was an issue displaying the detailed results.</p>
            <p>Your score: ${results?.score || 'N/A'} / ${results?.maxScore || 'N/A'}</p>
            <button onclick="location.reload()" class="modern-submit-btn">Refresh Page</button>
          </div>
        `;
        fallbackContainer.classList.remove('hidden');
      }
    }
  }

  /**
   * Set up fallback mechanism to prevent users from getting stuck on interactive questions
   */
  setupFallbackActivation(question) {
    // Clear any existing fallback timer
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
    }

    // For interactive questions, set up a fallback timer
    const interactiveTypes = ['drag-drop', 'number-line', 'area-model'];
    if (interactiveTypes.includes(question.type)) {
      console.log(`Setting up fallback activation for ${question.type} question`);

      // Enable next button after 30 seconds if user hasn't interacted
      this.fallbackTimer = setTimeout(() => {
        const nextBtn = document.getElementById('next-question-btn');
        if (nextBtn && nextBtn.disabled) {
          console.log(`Fallback activation triggered for ${question.type} question after 30 seconds`);
          this.setNextButtonState(true, `Fallback: Auto-enabled after 30s for ${question.type} question`);

          // Show a helpful message to the user
          this.showFallbackMessage(question.type);
        }
      }, 30000); // 30 seconds
    }
  }

  /**
   * Show a helpful message when fallback activation occurs
   */
  showFallbackMessage(questionType) {
    // Create or update a help message
    let helpMessage = document.getElementById('fallback-help-message');
    if (!helpMessage) {
      helpMessage = document.createElement('div');
      helpMessage.id = 'fallback-help-message';
      helpMessage.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 1000;
        max-width: 300px;
        font-size: 14px;
        line-height: 1.4;
      `;
      document.body.appendChild(helpMessage);
    }

    const messages = {
      'drag-drop': 'You can now proceed to the next question. Try dragging items to the drop zones for the best learning experience.',
      'number-line': 'You can now proceed to the next question. Try clicking or dragging on the number line for the best learning experience.',
      'area-model': 'You can now proceed to the next question. Try clicking on the shapes or segments for the best learning experience.'
    };

    helpMessage.innerHTML = `
      <strong>Help Available!</strong><br>
      ${messages[questionType] || 'You can now proceed to the next question.'}
      <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 16px; cursor: pointer;">&times;</button>
    `;

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (helpMessage && helpMessage.parentElement) {
        helpMessage.remove();
      }
    }, 10000);
  }

  /**
   * Clear fallback timer when user interacts with the question
   */
  clearFallbackTimer() {
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
      console.log('Fallback timer cleared due to user interaction');
    }
  }

  /**
   * Check if user can progress to next level
   */
  checkLevelProgression(results) {
    const progressionMap = {
      'Entry': 'Level1',
      'Level1': 'GCSEPart1',
      'GCSEPart1': 'GCSEPart2',
      'GCSEPart2': null // No further progression
    };

    const nextLevel = progressionMap[this.currentLevel];

    if (nextLevel && results.passed) {
      this.nextAvailableLevel = nextLevel;
      console.log(`User passed ${this.currentLevel}, can progress to ${nextLevel}`);
    }
  }

  /**
   * Add progression options to results
   */
  addProgressionOptions(results) {
    const actionsContainer = document.querySelector('.results-actions');
    if (!actionsContainer) {
      console.warn('Results actions container not found');
      return;
    }

    // Clear existing actions
    actionsContainer.innerHTML = '';

    // Add detailed report button
    const reportBtn = document.createElement('button');
    reportBtn.id = 'view-detailed-report-btn';
    reportBtn.className = 'modern-submit-btn';
    reportBtn.innerHTML = `
      <span class="btn-text">View Detailed Report</span>
      <span class="btn-icon">📊</span>
    `;
    reportBtn.addEventListener('click', () => this.showDetailedReportModal());
    actionsContainer.appendChild(reportBtn);

    // Add progression button if user passed and next level is available
    if (results.passed && this.nextAvailableLevel) {
      const progressBtn = document.createElement('button');
      progressBtn.className = 'modern-submit-btn progression-btn';
      progressBtn.innerHTML = `
        <span class="btn-text">Continue to ${this.nextAvailableLevel}</span>
        <span class="btn-icon">→</span>
      `;
      progressBtn.addEventListener('click', () => this.progressToNextLevel());
      actionsContainer.appendChild(progressBtn);
    }

    // Add retake button if user didn't pass
    if (!results.passed) {
      const retakeBtn = document.createElement('button');
      retakeBtn.className = 'modern-submit-btn retake-btn';
      retakeBtn.innerHTML = `
        <span class="btn-text">Retake Assessment</span>
        <span class="btn-icon">🔄</span>
      `;
      retakeBtn.addEventListener('click', () => this.retakeAssessment());
      actionsContainer.appendChild(retakeBtn);
    }
  }

  /**
   * Progress to next level
   */
  async progressToNextLevel() {
    if (!this.nextAvailableLevel) {
      alert('No next level available');
      return;
    }

    try {
      // Update current level
      this.currentLevel = this.nextAvailableLevel;
      this.nextAvailableLevel = null;

      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;

      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;

      // Show instructions for new level (this will handle hiding other screens)
      this.showInstructions();

    } catch (error) {
      console.error('Error progressing to next level:', error);
      alert('Failed to progress to next level. Please try again.');
    }
  }

  /**
   * Retake current assessment
   */
  async retakeAssessment() {
    try {
      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;
      this.timeRemaining = this.timeLimit;

      // Show instructions (this will handle hiding other screens)
      this.showInstructions();

    } catch (error) {
      console.error('Error retaking assessment:', error);
      alert('Failed to restart assessment. Please try again.');
    }
  }



  /**
   * Display topic performance breakdown
   */
  displayTopicBreakdown(topicBreakdown) {
    const container = document.getElementById('topic-breakdown');
    if (!container) {
      console.warn('Topic breakdown container not found');
      return;
    }

    container.innerHTML = '';

    // Calculate total possible points for percentage calculation
    const totalTopics = Object.keys(topicBreakdown).length;
    const maxPointsPerTopic = Math.floor(this.levelSpecs[this.currentLevel].maxScore / totalTopics);

    Object.entries(topicBreakdown).forEach(([topic, scoreData]) => {
      // Handle both object format {correct: 0, total: 0, percentage: 0} and number format
      let score, total, percentage;

      if (typeof scoreData === 'object' && scoreData !== null) {
        // New format from AI analysis
        score = scoreData.correct || 0;
        total = scoreData.total || maxPointsPerTopic;
        percentage = scoreData.percentage || 0;
      } else {
        // Legacy number format
        score = scoreData || 0;
        total = maxPointsPerTopic;
        percentage = Math.round((score / total) * 100);
      }

      const topicElement = document.createElement('div');
      topicElement.className = 'topic-item';

      // Add performance indicator
      let performanceClass = 'low';
      if (percentage >= 80) performanceClass = 'high';
      else if (percentage >= 60) performanceClass = 'medium';

      topicElement.innerHTML = `
        <div class="topic-info">
          <span class="topic-name">${this.formatTopicName(topic)}</span>
          <span class="topic-percentage ${performanceClass}">${percentage}%</span>
        </div>
        <div class="topic-progress-bar">
          <div class="topic-progress-fill ${performanceClass}" style="width: ${percentage}%"></div>
        </div>
        <span class="topic-score">${score}/${total}</span>
      `;
      container.appendChild(topicElement);
    });
  }

  /**
   * Display placement recommendations
   */
  displayRecommendations(recommendation) {
    const container = document.getElementById('recommendations');
    if (!container) {
      console.warn('Recommendations container not found');
      return;
    }

    container.innerHTML = `
      <div class="recommendation-header">
        <div class="recommendation-level">
          <span class="level-badge">${recommendation.level}</span>
          <span class="level-label">Recommended Level</span>
        </div>
      </div>

      <div class="recommendation-content">
        <div class="recommendation-reasoning">
          <h4>Assessment Summary</h4>
          <p>${recommendation.reasoning}</p>
        </div>

        <div class="recommendation-next-steps">
          <h4>Immediate Next Steps</h4>
          <ul class="next-steps-list">
            ${recommendation.nextSteps.map(step => `<li>${step}</li>`).join('')}
          </ul>
        </div>

        <div class="recommendation-courses">
          <h4>Recommended Courses</h4>
          <div class="course-list">
            ${recommendation.courseRecommendations.map(course => `
              <div class="course-item">
                <span class="course-icon">📚</span>
                <span class="course-name">${course}</span>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Display comprehensive feedback
   */
  displayFeedback(feedback) {
    const feedbackSection = document.querySelector('.feedback-section');
    if (!feedbackSection) {
      console.warn('Feedback section not found');
      return;
    }

    if (!feedback || Object.keys(feedback).length === 0) {
      feedbackSection.style.display = 'none';
      return;
    }

    const feedbackContainer = document.createElement('div');
    feedbackContainer.className = 'feedback-container';

    const feedbackAreas = [
      { key: 'numericalSkills', title: 'Numerical Skills', icon: '🔢' },
      { key: 'algebraicThinking', title: 'Algebraic Thinking', icon: '📐' },
      { key: 'problemSolving', title: 'Problem Solving', icon: '🧩' },
      { key: 'geometricReasoning', title: 'Geometric Reasoning', icon: '📏' },
      { key: 'dataHandling', title: 'Data Handling', icon: '📊' }
    ];

    feedbackAreas.forEach(area => {
      if (feedback[area.key]) {
        const feedbackItem = document.createElement('div');
        feedbackItem.className = 'feedback-item';
        feedbackItem.innerHTML = `
          <div class="feedback-header">
            <span class="feedback-icon">${area.icon}</span>
            <h4 class="feedback-title">${area.title}</h4>
          </div>
          <p class="feedback-text">${feedback[area.key]}</p>
        `;
        feedbackContainer.appendChild(feedbackItem);
      }
    });

    // Add overall feedback if available
    if (feedback.overall) {
      const overallFeedback = document.createElement('div');
      overallFeedback.className = 'feedback-item overall-feedback';
      overallFeedback.innerHTML = `
        <div class="feedback-header">
          <span class="feedback-icon">🎯</span>
          <h4 class="feedback-title">Overall Assessment</h4>
        </div>
        <p class="feedback-text">${feedback.overall}</p>
      `;
      feedbackContainer.appendChild(overallFeedback);
    }

    // Replace existing content
    feedbackSection.innerHTML = '<h3>Your Performance Analysis</h3>';
    feedbackSection.appendChild(feedbackContainer);
  }

  /**
   * Display strengths and improvements
   */
  displayStrengthsAndImprovements(strengths, improvements) {
    const container = document.querySelector('.feedback-section');
    if (!container) {
      console.warn('Feedback section not found for strengths and improvements');
      return;
    }

    if (strengths && strengths.length > 0) {
      const strengthsDiv = document.createElement('div');
      strengthsDiv.className = 'strengths-section';
      strengthsDiv.innerHTML = `
        <h4 class="section-title positive">✅ Your Strengths</h4>
        <ul class="strengths-list">
          ${strengths.map(strength => `<li class="strength-item">${strength}</li>`).join('')}
        </ul>
      `;
      container.appendChild(strengthsDiv);
    }

    if (improvements && improvements.length > 0) {
      const improvementsDiv = document.createElement('div');
      improvementsDiv.className = 'improvements-section';
      improvementsDiv.innerHTML = `
        <h4 class="section-title improvement">📈 Areas for Development</h4>
        <ul class="improvements-list">
          ${improvements.map(improvement => `<li class="improvement-item">${improvement}</li>`).join('')}
        </ul>
      `;
      container.appendChild(improvementsDiv);
    }
  }

  /**
   * Format topic name for display
   */
  formatTopicName(topic) {
    return topic.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }

  /**
   * Determine achievement level based on score percentage
   */
  determineAchievementLevel(score, maxScore) {
    const percentage = (score / maxScore) * 100;

    if (percentage >= 80) {
      return { level: 'Excellent Progress', class: 'excellent-progress' };
    } else if (percentage >= 60) {
      return { level: 'Good Progress', class: 'good-progress' };
    } else if (percentage >= 40) {
      return { level: 'Steady Progress', class: 'steady-progress' };
    } else if (percentage >= 20) {
      return { level: 'Foundation Building', class: 'foundation-building' };
    } else {
      return { level: 'Getting Started', class: 'getting-started' };
    }
  }

  /**
   * Add encouraging message based on achievement level
   */
  addEncouragingMessage(achievementLevel, results) {
    // Create or update encouraging message element
    let messageElement = document.getElementById('encouraging-message');
    if (!messageElement) {
      messageElement = document.createElement('div');
      messageElement.id = 'encouraging-message';
      messageElement.className = 'encouraging-message';

      // Insert after status badge
      const statusBadge = document.getElementById('pass-status');
      if (statusBadge && statusBadge.parentNode) {
        statusBadge.parentNode.insertBefore(messageElement, statusBadge.nextSibling);
      } else {
        console.warn('Status badge not found for encouraging message');
        return;
      }
    }

    // Generate encouraging message based on performance
    const messages = {
      'excellent-progress': [
        'Outstanding work! You\'re demonstrating strong mathematical skills.',
        'You\'re very close to mastering this level. Keep up the excellent progress!'
      ],
      'good-progress': [
        'Great job! You\'re making solid progress in your mathematical journey.',
        'You\'ve shown good understanding. A bit more practice will get you to the next level!'
      ],
      'steady-progress': [
        'Well done! You\'re building a solid foundation in mathematics.',
        'You\'re on the right track. Continue practicing to strengthen your skills!'
      ],
      'foundation-building': [
        'Good start! You\'re developing important mathematical foundations.',
        'Every step counts in your learning journey. Keep building those skills!'
      ],
      'getting-started': [
        'You\'ve taken the first step in your mathematical journey!',
        'Learning mathematics takes time. You\'re building important foundations!'
      ]
    };

    const levelMessages = messages[achievementLevel.class] || messages['getting-started'];
    const randomMessage = levelMessages[Math.floor(Math.random() * levelMessages.length)];

    messageElement.innerHTML = `
      <div class="message-content">
        <span class="message-icon">🌟</span>
        <p class="message-text">${randomMessage}</p>
      </div>
    `;
  }

  /**
   * Start the assessment timer
   */
  startTimer() {
    this.timerInterval = setInterval(() => {
      this.timeRemaining--;
      this.updateTimerDisplay();
      
      if (this.timeRemaining <= 0) {
        this.completeAssessment();
      }
    }, 1000);
  }

  /**
   * Stop the assessment timer
   */
  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update timer display
   */
  updateTimerDisplay() {
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('timer').textContent = timeString;
    document.getElementById('timer-display').textContent = timeString;
  }

  /**
   * Show loading screen with enhanced animations
   */
  showLoading(message) {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show loading screen
    document.getElementById('assessment-loading').classList.remove('hidden');
    document.querySelector('.loading-text').textContent = message;

    // Start progress animation
    this.animateProgressSteps();
  }

  /**
   * Animate progress steps during loading
   */
  animateProgressSteps() {
    const steps = document.querySelectorAll('.step');
    if (steps.length === 0) return;

    // Reset all steps
    steps.forEach(step => {
      step.classList.remove('active', 'completed');
    });

    // Animate steps sequentially
    let currentStep = 0;
    const stepInterval = setInterval(() => {
      if (currentStep > 0) {
        steps[currentStep - 1].classList.remove('active');
        steps[currentStep - 1].classList.add('completed');
      }

      if (currentStep < steps.length) {
        steps[currentStep].classList.add('active');
        currentStep++;
      } else {
        clearInterval(stepInterval);
        // Mark last step as completed after a brief delay
        setTimeout(() => {
          if (steps[steps.length - 1]) {
            steps[steps.length - 1].classList.remove('active');
            steps[steps.length - 1].classList.add('completed');
          }
        }, 500);
      }
    }, 1000); // Change step every second

    // Store interval reference for cleanup
    this.progressInterval = stepInterval;
  }

  /**
   * Hide loading screen
   */
  hideLoading() {
    document.getElementById('assessment-loading').classList.add('hidden');

    // Clean up progress animation
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }

    // Clean up loading message interval
    if (this.loadingMessageInterval) {
      clearInterval(this.loadingMessageInterval);
      this.loadingMessageInterval = null;
    }
  }

  /**
   * Hide user form
   */
  hideUserForm() {
    const userFormContainer = document.getElementById('user-form-container');
    if (userFormContainer) {
      userFormContainer.classList.add('hidden');
    }
  }

  /**
   * Test method to verify container visibility flow
   * Can be called from browser console: window.mathAssessment.testContainerFlow()
   */
  testContainerFlow() {
    console.log('Testing container visibility flow...');

    const containers = [
      'user-form-container',
      'math-assessment-container',
      'assessment-instructions',
      'assessment-questions',
      'assessment-results',
      'assessment-loading',
      'header'
    ];

    const getVisibilityState = () => {
      const state = {};
      containers.forEach(id => {
        const element = document.getElementById(id);
        state[id] = element ? !element.classList.contains('hidden') : 'not found';
      });
      return state;
    };

    console.log('1. Initial state:', getVisibilityState());

    // Test reset to initial state
    this.resetToInitialState();
    console.log('2. After resetToInitialState():', getVisibilityState());

    // Test show instructions
    this.currentLevel = 'Entry';
    this.showInstructions();
    console.log('3. After showInstructions():', getVisibilityState());

    // Test show loading
    this.showLoading('Test loading...');
    console.log('4. After showLoading():', getVisibilityState());

    // Test hide loading and show questions
    this.hideLoading();
    this.showQuestions();
    console.log('5. After showQuestions():', getVisibilityState());

    // Reset back to initial state
    this.resetToInitialState();
    console.log('6. Final reset to initial state:', getVisibilityState());

    console.log('Container flow test completed!');
  }

  /**
   * Test responsive results display
   * Can be called from browser console: window.mathAssessment.testResponsiveResults()
   */
  testResponsiveResults() {
    console.log('Testing responsive results display...');

    // Create mock results data for testing (you can modify score to test different achievement levels)
    const mockResults = {
      score: 18, // Try different values: 35+ (passed), 25-34 (excellent), 15-24 (good), 10-14 (steady), 5-9 (foundation), 0-4 (getting started)
      maxScore: 44,
      passed: false, // Will be determined by score vs passing threshold
      topicBreakdown: {
        arithmetic: 8,
        algebra: 6,
        geometry: 4,
        statistics: 5,
        problemSolving: 3,
        measurement: 2
      },
      feedback: {
        numericalSkills: 'Strong performance in basic arithmetic operations. Shows good understanding of number relationships and calculation accuracy.',
        algebraicThinking: 'Demonstrates solid grasp of algebraic concepts. Can solve simple equations and work with variables effectively.',
        problemSolving: 'Good analytical approach to word problems. Shows ability to break down complex scenarios into manageable steps.',
        geometricReasoning: 'Basic understanding of geometric shapes and properties. Could benefit from more practice with angle calculations.',
        dataHandling: 'Competent in reading and interpreting basic charts and graphs. Shows understanding of averages and data trends.',
        overall: 'Excellent overall performance demonstrating readiness for Level 1 mathematics. Strong foundation across multiple topic areas with particular strength in arithmetic and algebra.'
      },
      strengths: [
        'Excellent arithmetic calculation skills',
        'Strong algebraic reasoning and equation solving',
        'Good problem-solving methodology',
        'Effective time management during assessment'
      ],
      improvements: [
        'Practice geometric angle calculations',
        'Develop statistical analysis skills',
        'Work on complex word problem interpretation',
        'Strengthen measurement unit conversions'
      ],
      placementRecommendation: {
        level: 'Level 1',
        reasoning: 'Strong performance across multiple mathematical areas indicates readiness for Level 1 mathematics course. Particular strengths in arithmetic and algebra provide excellent foundation for progression.',
        nextSteps: [
          'Enroll in Level 1 Mathematics course',
          'Focus on geometry and statistics modules',
          'Practice advanced problem-solving techniques'
        ],
        courseRecommendations: [
          'Functional Skills Mathematics Level 1',
          'GCSE Mathematics Foundation Tier',
          'Adult Numeracy Level 1 Course'
        ]
      }
    };

    // Show the results with mock data
    this.showResults(mockResults);

    console.log('Mock results displayed. Check responsive behavior:');
    console.log('1. Results container should have scrolling if content exceeds viewport');
    console.log('2. All sections should be accessible through scrolling');
    console.log('3. Layout should adapt to screen size');
    console.log('4. Action buttons should be visible and accessible');

    // Test viewport dimensions
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio
    };

    console.log('Current viewport:', viewport);

    if (viewport.width < 768) {
      console.log('📱 Mobile viewport detected - verify mobile-specific styles');
    } else if (viewport.width < 1024) {
      console.log('📱 Tablet viewport detected - verify tablet-specific styles');
    } else {
      console.log('🖥️ Desktop viewport detected - verify desktop layout');
    }

    return mockResults;
  }

  /**
   * Show detailed report modal
   */
  showDetailedReportModal() {
    const modal = document.getElementById('detailed-report-modal');
    if (!modal) {
      console.error('Modal element not found');
      return;
    }

    // Populate modal with current assessment data
    this.populateDetailedReport();

    // Show modal with animation
    modal.classList.remove('hidden');
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  /**
   * Hide detailed report modal
   */
  hideDetailedReportModal() {
    const modal = document.getElementById('detailed-report-modal');
    if (!modal) return;

    // Hide modal with animation
    modal.classList.remove('show');
    setTimeout(() => {
      modal.classList.add('hidden');
    }, 300);

    // Restore body scroll
    document.body.style.overflow = '';
  }

  /**
   * Populate detailed report modal with assessment data
   */
  populateDetailedReport() {
    if (!this.assessmentResults) {
      console.warn('No assessment results available for detailed report');
      return;
    }

    const results = this.assessmentResults;

    // Populate summary section
    this.populateModalSummary(results);

    // Populate topic breakdown
    this.populateModalTopicBreakdown(results);

    // Populate feedback
    this.populateModalFeedback(results);

    // Populate strengths
    this.populateModalStrengths(results);

    // Populate improvements
    this.populateModalImprovements(results);

    // Populate recommendations
    this.populateModalRecommendations(results);

    // Populate course recommendations
    this.populateModalCourses(results);
  }

  /**
   * Populate modal summary section
   */
  populateModalSummary(results) {
    const elements = {
      level: document.getElementById('modal-level'),
      score: document.getElementById('modal-score'),
      percentage: document.getElementById('modal-percentage'),
      status: document.getElementById('modal-status'),
      time: document.getElementById('modal-time'),
      questions: document.getElementById('modal-questions')
    };

    if (elements.level) elements.level.textContent = this.assessmentLevel || 'Entry';
    if (elements.score) elements.score.textContent = `${results.score || 0} / ${results.maxScore || 44}`;
    if (elements.percentage) {
      const percentage = Math.round(((results.score || 0) / (results.maxScore || 44)) * 100);
      elements.percentage.textContent = `${percentage}%`;
    }
    if (elements.status) {
      // Since we're showing results, the assessment is complete
      const statusText = results.passed ? 'Passed' : 'Assessment Complete';
      const statusClass = results.passed ? 'status-passed' : 'status-complete';
      elements.status.textContent = statusText;
      elements.status.className = `summary-value ${statusClass}`;
    }
    if (elements.time) {
      // Calculate time spent from timer (timeLimit - timeRemaining)
      const timeSpent = this.timeLimit - this.timeRemaining;
      const minutes = Math.floor(timeSpent / 60);
      elements.time.textContent = `${minutes} minutes`;
    }
    if (elements.questions) {
      const specs = this.levelSpecs[this.assessmentLevel] || this.levelSpecs['Entry'];
      elements.questions.textContent = specs.questionCount;
    }
  }

  /**
   * Populate modal topic breakdown
   */
  populateModalTopicBreakdown(results) {
    const container = document.getElementById('modal-topic-breakdown');
    if (!container || !results.topicBreakdown) return;

    container.innerHTML = '';

    Object.entries(results.topicBreakdown).forEach(([topic, scoreData]) => {
      const topicItem = document.createElement('div');
      topicItem.className = 'topic-item-detailed';

      // Handle both object format {correct: 0, total: 0, percentage: 0} and number format
      let score, maxScore, percentage;

      if (typeof scoreData === 'object' && scoreData !== null) {
        // New format from AI analysis
        score = scoreData.correct || 0;
        maxScore = scoreData.total || this.getMaxScoreForTopic(topic);
        percentage = scoreData.percentage || 0;
      } else {
        // Legacy number format
        score = scoreData || 0;
        maxScore = this.getMaxScoreForTopic(topic);
        percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
      }

      topicItem.innerHTML = `
        <div class="topic-header">
          <span class="topic-name">${this.formatTopicName(topic)}</span>
          <span class="topic-score">${score} / ${maxScore}</span>
        </div>
        <div class="topic-progress">
          <div class="topic-progress-fill" style="width: ${percentage}%"></div>
        </div>
        <div class="topic-description">
          ${this.getTopicDescription(topic)}
        </div>
      `;

      container.appendChild(topicItem);
    });
  }

  /**
   * Populate modal feedback section
   */
  populateModalFeedback(results) {
    const container = document.getElementById('modal-feedback');
    if (!container || !results.feedback) return;

    container.innerHTML = '';

    Object.entries(results.feedback).forEach(([category, text]) => {
      const feedbackItem = document.createElement('div');
      feedbackItem.className = 'feedback-item';

      feedbackItem.innerHTML = `
        <div class="feedback-category">${this.formatFeedbackCategory(category)}</div>
        <div class="feedback-text">${text}</div>
      `;

      container.appendChild(feedbackItem);
    });
  }

  /**
   * Populate modal strengths section
   */
  populateModalStrengths(results) {
    const container = document.getElementById('modal-strengths');
    if (!container || !results.strengths) return;

    container.innerHTML = '';

    results.strengths.forEach(strength => {
      const strengthItem = document.createElement('div');
      strengthItem.className = 'strength-item';

      strengthItem.innerHTML = `
        <div class="item-icon">✅</div>
        <div class="item-text">${strength}</div>
      `;

      container.appendChild(strengthItem);
    });
  }

  /**
   * Populate modal improvements section
   */
  populateModalImprovements(results) {
    const container = document.getElementById('modal-improvements');
    if (!container || !results.improvements) return;

    container.innerHTML = '';

    results.improvements.forEach(improvement => {
      const improvementItem = document.createElement('div');
      improvementItem.className = 'improvement-item';

      improvementItem.innerHTML = `
        <div class="item-icon">🎯</div>
        <div class="item-text">${improvement}</div>
      `;

      container.appendChild(improvementItem);
    });
  }

  /**
   * Populate modal recommendations section
   */
  populateModalRecommendations(results) {
    const container = document.getElementById('modal-recommendations');
    if (!container || !results.placementRecommendation) return;

    container.innerHTML = '';

    const recommendation = results.placementRecommendation;

    const recommendationItem = document.createElement('div');
    recommendationItem.className = 'recommendation-item';

    recommendationItem.innerHTML = `
      <div class="recommendation-title">Recommended Level: ${recommendation.level}</div>
      <div class="recommendation-description">${recommendation.reasoning}</div>
    `;

    container.appendChild(recommendationItem);

    // Add next steps if available
    if (recommendation.nextSteps && recommendation.nextSteps.length > 0) {
      const nextStepsItem = document.createElement('div');
      nextStepsItem.className = 'recommendation-item';

      const nextStepsList = recommendation.nextSteps.map(step => `<li>${step}</li>`).join('');
      nextStepsItem.innerHTML = `
        <div class="recommendation-title">Next Steps</div>
        <div class="recommendation-description">
          <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
            ${nextStepsList}
          </ul>
        </div>
      `;

      container.appendChild(nextStepsItem);
    }
  }

  /**
   * Populate modal courses section
   */
  populateModalCourses(results) {
    const container = document.getElementById('modal-courses');
    if (!container) return;

    container.innerHTML = '';

    // Get course recommendations from placement recommendation
    const courseRecommendations = results.placementRecommendation?.courseRecommendations || [];

    if (courseRecommendations.length === 0) {
      container.innerHTML = '<div class="course-item"><div class="course-title">No specific course recommendations available</div></div>';
      return;
    }

    courseRecommendations.forEach(course => {
      const courseItem = document.createElement('div');
      courseItem.className = 'course-item';

      courseItem.innerHTML = `
        <div class="course-title">${course}</div>
        <div class="course-description">Recommended based on your assessment results</div>
      `;

      container.appendChild(courseItem);
    });
  }

  /**
   * Helper methods for formatting and data processing
   */
  formatTopicName(topic) {
    const topicNames = {
      arithmetic: 'Arithmetic',
      fractions: 'Fractions',
      percentages: 'Percentages',
      basicAlgebra: 'Basic Algebra',
      measurement: 'Measurement',
      dataHandling: 'Data Handling',
      advancedArithmetic: 'Advanced Arithmetic',
      fractionsDecimals: 'Fractions & Decimals',
      percentagesRatio: 'Percentages & Ratio',
      algebraicExpressions: 'Algebraic Expressions',
      geometry: 'Geometry',
      statistics: 'Statistics'
    };
    return topicNames[topic] || topic.charAt(0).toUpperCase() + topic.slice(1);
  }

  formatFeedbackCategory(category) {
    const categoryNames = {
      numericalSkills: 'Numerical Skills',
      algebraicThinking: 'Algebraic Thinking',
      problemSolving: 'Problem Solving',
      geometricReasoning: 'Geometric Reasoning',
      dataHandling: 'Data Handling',
      overall: 'Overall Assessment'
    };
    return categoryNames[category] || category.charAt(0).toUpperCase() + category.slice(1);
  }

  getMaxScoreForTopic(topic) {
    // This would typically come from the assessment configuration
    // For now, return a reasonable default based on typical question distribution
    const topicMaxScores = {
      arithmetic: 8,
      fractions: 6,
      percentages: 4,
      basicAlgebra: 6,
      measurement: 4,
      dataHandling: 4,
      advancedArithmetic: 6,
      fractionsDecimals: 4,
      percentagesRatio: 4,
      algebraicExpressions: 6,
      geometry: 4,
      statistics: 4
    };
    return topicMaxScores[topic] || 4;
  }

  getTopicDescription(topic) {
    const descriptions = {
      arithmetic: 'Basic mathematical operations including addition, subtraction, multiplication, and division',
      fractions: 'Understanding and working with fractions, including equivalent fractions and basic operations',
      percentages: 'Converting between percentages, decimals, and fractions, and solving percentage problems',
      basicAlgebra: 'Introduction to algebraic concepts including variables, simple equations, and expressions',
      measurement: 'Units of measurement, conversions, and practical measurement applications',
      dataHandling: 'Reading and interpreting charts, graphs, and basic statistical concepts'
    };
    return descriptions[topic] || 'Mathematical concepts and problem-solving skills';
  }



  /**
   * Test function to verify modal functionality
   */
  testModalFunctionality() {
    console.log('Testing modal functionality...');

    // Create mock results if none exist
    if (!this.assessmentResults) {
      console.log('Creating mock assessment results...');
      this.assessmentResults = {
        score: 20,
        maxScore: 44,
        passed: false,
        topicBreakdown: {
          arithmetic: 6,
          fractions: 4,
          percentages: 2,
          basicAlgebra: 3,
          measurement: 3,
          dataHandling: 2
        },
        feedback: {
          numericalSkills: "Good understanding of basic arithmetic operations.",
          algebraicThinking: "Needs improvement in algebraic concepts and problem-solving.",
          problemSolving: "Shows potential but requires more practice with word problems.",
          overall: "Solid foundation with room for improvement in advanced topics."
        },
        strengths: [
          "Strong performance in basic arithmetic calculations",
          "Good understanding of measurement concepts",
          "Consistent approach to problem-solving"
        ],
        improvements: [
          "Practice more fraction operations and conversions",
          "Work on percentage calculations and applications",
          "Strengthen algebraic thinking and equation solving"
        ],
        placementRecommendation: {
          level: "Entry Support",
          reasoning: "Student shows good foundational skills but needs additional support in key areas before progressing.",
          nextSteps: [
            "Focus on fraction operations and decimal conversions",
            "Practice percentage problems in real-world contexts",
            "Build confidence with basic algebraic concepts"
          ],
          courseRecommendations: [
            "Entry Level Functional Skills Mathematics",
            "Basic Numeracy Workshop",
            "Fractions and Decimals Masterclass"
          ]
        }
      };

      this.assessmentLevel = 'Entry';
      // Set up time tracking for test (simulate 25 minutes spent)
      this.timeLimit = 30 * 60; // 30 minutes
      this.timeRemaining = 5 * 60; // 5 minutes remaining = 25 minutes spent
    }

    console.log('Opening modal...');
    this.showDetailedReportModal();

    return 'Modal test initiated. Check if modal opened correctly.';
  }
}

/**
 * Calculator Widget Class
 */
class Calculator {
  constructor() {
    this.reset();
  }

  /**
   * Reset calculator to initial state
   */
  reset() {
    this.currentValue = '0';
    this.previousValue = null;
    this.operation = null;
    this.waitingForNewValue = false;
    this.error = false;
    this.angleMode = 'degrees'; // degrees or radians
    this.parenthesesStack = [];
    this.expression = '';

    // Initialize memory if not already set
    if (this.memory === undefined) {
      this.memory = 0;
    }
  }

  /**
   * Input a number
   */
  inputNumber(num) {
    if (this.error) {
      this.reset();
    }

    if (this.waitingForNewValue) {
      this.currentValue = num;
      this.waitingForNewValue = false;
    } else {
      this.currentValue = this.currentValue === '0' ? num : this.currentValue + num;
    }

    // Limit display length
    if (this.currentValue.length > 12) {
      this.currentValue = this.currentValue.slice(0, 12);
    }
  }

  /**
   * Input an operation
   */
  inputOperation(nextOperation) {
    if (this.error) {
      this.reset();
      return;
    }

    const inputValue = parseFloat(this.currentValue);

    if (this.previousValue === null) {
      this.previousValue = inputValue;
    } else if (this.operation) {
      const currentValue = this.previousValue || 0;
      const newValue = this.calculate(currentValue, inputValue, this.operation);

      this.currentValue = String(newValue);
      this.previousValue = newValue;
    }

    this.waitingForNewValue = true;
    this.operation = nextOperation;
  }

  /**
   * Perform calculator actions
   */
  performAction(action) {
    switch (action) {
      case 'clear':
        this.reset();
        break;
      case 'clear-entry':
        this.currentValue = '0';
        break;
      case 'backspace':
        if (this.error) {
          this.reset();
          return;
        }
        if (this.currentValue.length > 1) {
          this.currentValue = this.currentValue.slice(0, -1);
        } else {
          this.currentValue = '0';
        }
        break;
      case 'decimal':
        if (this.error) {
          this.reset();
        }
        if (this.waitingForNewValue) {
          this.currentValue = '0.';
          this.waitingForNewValue = false;
        } else if (this.currentValue.indexOf('.') === -1) {
          this.currentValue += '.';
        }
        break;
      case 'equals':
        this.performEquals();
        break;
    }
  }

  /**
   * Perform equals calculation
   */
  performEquals() {
    if (this.error) {
      this.reset();
      return;
    }

    const inputValue = parseFloat(this.currentValue);

    if (this.previousValue !== null && this.operation) {
      const newValue = this.calculate(this.previousValue, inputValue, this.operation);
      this.currentValue = String(newValue);
      this.previousValue = null;
      this.operation = null;
      this.waitingForNewValue = true;
    }
  }

  /**
   * Perform calculation
   */
  calculate(firstValue, secondValue, operation) {
    let result;

    switch (operation) {
      case '+':
        result = firstValue + secondValue;
        break;
      case '-':
        result = firstValue - secondValue;
        break;
      case '*':
        result = firstValue * secondValue;
        break;
      case '/':
        if (secondValue === 0) {
          this.error = true;
          return 'Error';
        }
        result = firstValue / secondValue;
        break;
      case '^':
        result = this.performPower(firstValue, secondValue);
        if (result === 'Error') {
          return 'Error';
        }
        break;
      default:
        return secondValue;
    }

    // Handle very large or very small numbers
    if (!isFinite(result)) {
      this.error = true;
      return 'Error';
    }

    // Round to avoid floating point precision issues
    result = Math.round(result * 1000000000000) / 1000000000000;

    // Format result for display
    if (Math.abs(result) > 999999999999 || (Math.abs(result) < 0.000000000001 && result !== 0)) {
      result = result.toExponential(6);
    } else {
      result = parseFloat(result.toPrecision(12));
    }

    return result;
  }

  /**
   * Get display value with Casio-style formatting
   */
  getDisplayValue() {
    if (this.error) {
      return 'Math ERROR';
    }

    // Format the display value like a Casio calculator
    return this.formatCasioDisplay(this.currentValue);
  }

  /**
   * Format display value in Casio calculator style
   */
  formatCasioDisplay(value) {
    if (value === '0') {
      return '0';
    }

    const num = parseFloat(value);

    // Handle very large numbers with scientific notation (Casio style)
    if (Math.abs(num) >= 1e10) {
      return num.toExponential(9).replace('e+', 'E').replace('e-', 'E-');
    }

    // Handle very small numbers with scientific notation
    if (Math.abs(num) < 1e-9 && num !== 0) {
      return num.toExponential(9).replace('e+', 'E').replace('e-', 'E-');
    }

    // For normal numbers, limit to 10 digits total (like Casio)
    if (Math.abs(num) >= 1) {
      // Integer part + decimal part should not exceed 10 digits
      const integerPart = Math.floor(Math.abs(num)).toString();
      if (integerPart.length >= 10) {
        return num.toExponential(9).replace('e+', 'E').replace('e-', 'E-');
      }

      const maxDecimals = 10 - integerPart.length;
      return parseFloat(num.toFixed(maxDecimals)).toString();
    } else {
      // For numbers less than 1, show up to 10 significant digits
      return parseFloat(num.toPrecision(10)).toString();
    }
  }

  /**
   * Check if calculator has error
   */
  hasError() {
    return this.error;
  }

  /**
   * Perform scientific function
   */
  performFunction(functionName) {
    if (this.error) {
      this.reset();
      return;
    }

    const currentValue = parseFloat(this.currentValue);
    let result;

    try {
      switch (functionName) {
        // Trigonometric functions
        case 'sin':
          result = this.angleMode === 'degrees'
            ? Math.sin(currentValue * Math.PI / 180)
            : Math.sin(currentValue);
          break;
        case 'cos':
          result = this.angleMode === 'degrees'
            ? Math.cos(currentValue * Math.PI / 180)
            : Math.cos(currentValue);
          break;
        case 'tan':
          result = this.angleMode === 'degrees'
            ? Math.tan(currentValue * Math.PI / 180)
            : Math.tan(currentValue);
          break;
        case 'asin':
          result = Math.asin(currentValue);
          result = this.angleMode === 'degrees' ? result * 180 / Math.PI : result;
          break;
        case 'acos':
          result = Math.acos(currentValue);
          result = this.angleMode === 'degrees' ? result * 180 / Math.PI : result;
          break;
        case 'atan':
          result = Math.atan(currentValue);
          result = this.angleMode === 'degrees' ? result * 180 / Math.PI : result;
          break;

        // Logarithmic functions
        case 'log':
          result = Math.log10(currentValue);
          break;
        case 'ln':
          result = Math.log(currentValue);
          break;
        case 'log2':
          result = Math.log2(currentValue);
          break;

        // Exponential functions
        case 'square':
          result = currentValue * currentValue;
          break;
        case 'cube':
          result = currentValue * currentValue * currentValue;
          break;
        case 'sqrt':
          result = Math.sqrt(currentValue);
          break;
        case 'cbrt':
          result = Math.cbrt(currentValue);
          break;
        case 'exp':
          result = Math.exp(currentValue);
          break;
        case 'pow10':
          result = Math.pow(10, currentValue);
          break;
        case 'reciprocal':
          if (currentValue === 0) {
            this.error = true;
            return 'Error';
          }
          result = 1 / currentValue;
          break;

        // Other functions
        case 'factorial':
          if (currentValue < 0 || !Number.isInteger(currentValue) || currentValue > 170) {
            this.error = true;
            return 'Error';
          }
          result = this.factorial(currentValue);
          break;
        case 'percentage':
          result = currentValue / 100;
          break;
        case 'abs':
          result = Math.abs(currentValue);
          break;

        // Constants
        case 'pi':
          result = Math.PI;
          break;
        case 'e':
          result = Math.E;
          break;

        default:
          this.error = true;
          return 'Error';
      }

      // Handle invalid results
      if (!isFinite(result) || isNaN(result)) {
        this.error = true;
        return 'Error';
      }

      // Format result
      result = this.formatResult(result);
      this.currentValue = String(result);
      this.waitingForNewValue = true;

    } catch (error) {
      this.error = true;
      return 'Error';
    }
  }

  /**
   * Calculate factorial
   */
  factorial(n) {
    if (n === 0 || n === 1) return 1;
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  }

  /**
   * Format result for display
   */
  formatResult(result) {
    // Handle very large or very small numbers
    if (Math.abs(result) > 999999999999 || (Math.abs(result) < 0.000000000001 && result !== 0)) {
      return parseFloat(result.toExponential(6));
    }

    // Round to avoid floating point precision issues
    result = Math.round(result * 1000000000000) / 1000000000000;

    return parseFloat(result.toPrecision(12));
  }

  /**
   * Handle memory functions
   */
  performMemoryAction(action) {
    const currentValue = parseFloat(this.currentValue);

    switch (action) {
      case 'memory-clear':
        this.memory = 0;
        break;
      case 'memory-recall':
        this.currentValue = String(this.memory);
        this.waitingForNewValue = true;
        break;
      case 'memory-add':
        this.memory += currentValue;
        break;
      case 'memory-subtract':
        this.memory -= currentValue;
        break;
    }
  }

  /**
   * Handle parentheses
   */
  handleParenthesis(type) {
    if (type === 'open') {
      this.parenthesesStack.push('(');
      this.expression += '(';
    } else if (type === 'close' && this.parenthesesStack.length > 0) {
      this.parenthesesStack.pop();
      this.expression += ')';
    }
  }

  /**
   * Handle power operation (x^y)
   */
  performPower(base, exponent) {
    try {
      const result = Math.pow(base, exponent);
      if (!isFinite(result) || isNaN(result)) {
        this.error = true;
        return 'Error';
      }
      return this.formatResult(result);
    } catch (error) {
      this.error = true;
      return 'Error';
    }
  }
}

// Global cache warming function
async function warmMathAssessmentCache() {
  try {
    const baseUrl = window.location.protocol === 'file:'
      ? 'http://localhost:3003'
      : window.location.origin;

    console.log('🔥 Starting background cache warming for mathematics assessment...');

    // Warm cache for all levels in background
    fetch(`${baseUrl}/api/math-assessments/cache/warm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        levels: ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'],
        background: true
      }),
    }).then(response => {
      if (response.ok) {
        console.log('✅ Background cache warming initiated successfully');
      }
    }).catch(error => {
      console.warn('⚠️ Background cache warming failed:', error);
    });
  } catch (error) {
    console.warn('Error in cache warming:', error);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('Mathematics Assessment page loaded');

  // Initialize the assessment if not already done
  if (!window.mathAssessment) {
    window.mathAssessment = new MathAssessment();
  }

  // Start background cache warming after a short delay
  setTimeout(warmMathAssessmentCache, 1000);
});
