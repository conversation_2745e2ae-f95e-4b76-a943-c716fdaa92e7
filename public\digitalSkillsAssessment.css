/**
 * Digital Skills Assessment Styling
 * Following mathInteractive.css patterns with blue theme
 * Colors: #1547bb (primary blue), #121c41 (dark blue)
 */

/* ============================================================================
   ASSESSMENT CONTAINER STYLES
   ============================================================================ */

#digital-skills-assessment-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1547bb 0%, #121c41 100%);
  position: relative;
  overflow: hidden;
}

.assessment-screen {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

/* ============================================================================
   INSTRUCTION SCREEN STYLES
   ============================================================================ */

.instruction-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(21, 71, 187, 0.2);
  max-width: 700px;
  width: 100%;
  text-align: center;
}

.instruction-title {
  color: #121c41;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.instruction-content {
  margin: 30px 0;
}

.instruction-item {
  display: flex;
  align-items: center;
  margin: 20px 0;
  padding: 15px;
  background: #f8f9ff;
  border-radius: 12px;
  border-left: 4px solid #1547bb;
  text-align: left;
}

.instruction-icon {
  font-size: 1.5rem;
  margin-right: 15px;
  min-width: 30px;
}

.instruction-item p {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  line-height: 1.5;
}

.instruction-actions {
  margin-top: 40px;
}

.begin-btn {
  background: linear-gradient(135deg, #1547bb 0%, #0d3a8a 100%);
  color: #ffffff;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.3);
}

.begin-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(21, 71, 187, 0.4);
  background: linear-gradient(135deg, #0d3a8a 0%, #1547bb 100%);
}

.begin-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.3);
}

/* ============================================================================
   QUESTION SCREEN STYLES
   ============================================================================ */

.question-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(21, 71, 187, 0.2);
  max-width: 800px;
  width: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.question-header {
  margin-bottom: 30px;
}

.question-progress {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1547bb 0%, #0d3a8a 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  color: #121c41;
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
}

.question-content {
  flex: 1;
  margin-bottom: 30px;
}

.question-title {
  color: #121c41;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 25px;
  line-height: 1.6;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-option {
  position: relative;
}

.question-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.option-label {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9ff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 15px;
}

.option-label:hover {
  border-color: #1547bb;
  background: #f0f4ff;
  transform: translateX(4px);
}

.question-option input[type="radio"]:checked + .option-label {
  border-color: #1547bb;
  background: #e8f0ff;
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.15);
}

.option-letter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #1547bb;
  color: #ffffff;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.question-option input[type="radio"]:checked + .option-label .option-letter {
  background: #121c41;
}

.option-text {
  flex: 1;
  color: #333;
  font-size: 1rem;
  line-height: 1.5;
}

.question-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  margin-top: auto;
}

.nav-btn, .submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.nav-btn {
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.nav-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.submit-btn, .action-btn.primary {
  background: linear-gradient(135deg, #1547bb 0%, #0d3a8a 100%);
  color: #ffffff;
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.3);
}

.submit-btn:hover, .action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(21, 71, 187, 0.4);
}

/* ============================================================================
   RESULTS SCREEN STYLES
   ============================================================================ */

.results-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(21, 71, 187, 0.2);
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.results-header {
  margin-bottom: 30px;
}

.results-title {
  color: #121c41;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.results-score {
  display: inline-flex;
  align-items: baseline;
  gap: 5px;
  padding: 20px 30px;
  border-radius: 12px;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.results-score.passed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
}

.results-score.needs-improvement {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
}

.score-total {
  font-size: 1.5rem;
  opacity: 0.8;
}

.results-content {
  text-align: left;
  margin-bottom: 30px;
}

.results-summary {
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 25px;
  text-align: center;
}

.results-summary.passed {
  background: #ecfdf5;
  border: 2px solid #10b981;
}

.results-summary.needs-improvement {
  background: #fffbeb;
  border: 2px solid #f59e0b;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.results-feedback {
  color: #374151;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.results-details {
  margin-top: 25px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.metric {
  background: #f8f9ff;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #1547bb;
}

.metric-label {
  display: block;
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.metric-value {
  display: block;
  color: #121c41;
  font-size: 1.3rem;
  font-weight: 600;
}

.results-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.action-btn.secondary:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

/* ============================================================================
   LOADING OVERLAY STYLES
   ============================================================================ */

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(18, 28, 65, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #ffffff;
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.loading-animation {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #1547bb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  color: #121c41;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.loading-message {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 20px;
}

.loading-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading-progress .progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.loading-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1547bb 0%, #0d3a8a 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.loading-progress .progress-text {
  color: #121c41;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 35px;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  .assessment-screen {
    padding: 15px;
  }
  
  .instruction-container,
  .question-container,
  .results-container {
    padding: 25px;
  }
  
  .instruction-title,
  .results-title {
    font-size: 2rem;
  }
  
  .question-title {
    font-size: 1.2rem;
  }
  
  .question-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .nav-btn,
  .submit-btn,
  .action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
  
  .results-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .instruction-container,
  .question-container,
  .results-container {
    padding: 20px;
  }
  
  .instruction-title,
  .results-title {
    font-size: 1.8rem;
  }
  
  .results-score {
    font-size: 1.5rem;
    padding: 15px 20px;
  }
  
  .loading-content {
    padding: 30px 20px;
  }
}

/* ============================================================================
   TOPIC BREAKDOWN STYLES
   ============================================================================ */

.topic-breakdown {
  margin: 25px 0;
}

.breakdown-title {
  color: #121c41;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

.topic-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.topic-item {
  background: #f8f9ff;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s ease;
}

.topic-item.good {
  border-left-color: #10b981;
  background: #ecfdf5;
}

.topic-item.average {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.topic-item.needs-work {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.topic-name {
  color: #121c41;
  font-weight: 600;
  font-size: 1rem;
}

.topic-score {
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
}

.topic-progress {
  margin-top: 8px;
}

.topic-progress .progress-bar {
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.topic-item.good .topic-progress .progress-fill {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.topic-item.average .topic-progress .progress-fill {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.topic-item.needs-work .topic-progress .progress-fill {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

/* ============================================================================
   RECOMMENDATIONS STYLES
   ============================================================================ */

.recommendations-section {
  margin: 20px 0;
  padding: 20px;
  border-radius: 12px;
  background: #f8f9ff;
  border-left: 4px solid #1547bb;
}

.recommendations-section.strengths {
  background: #ecfdf5;
  border-left-color: #10b981;
}

.recommendations-section.improvements {
  background: #fffbeb;
  border-left-color: #f59e0b;
}

.recommendations-section.recommendations {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.recommendations-section.next-steps {
  background: #f3e8ff;
  border-left-color: #8b5cf6;
}

.section-title {
  color: #121c41;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation-list li {
  color: #374151;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.recommendation-list li:before {
  content: "•";
  color: #1547bb;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.recommendations-section.strengths .recommendation-list li:before {
  color: #10b981;
}

.recommendations-section.improvements .recommendation-list li:before {
  color: #f59e0b;
}

.recommendations-section.recommendations .recommendation-list li:before {
  color: #3b82f6;
}

.recommendations-section.next-steps .recommendation-list li:before {
  color: #8b5cf6;
}

/* ============================================================================
   TIMER WARNING STYLES
   ============================================================================ */

.timer-warning {
  color: #f59e0b !important;
  animation: pulse-warning 2s infinite;
}

.timer-critical {
  color: #ef4444 !important;
  animation: pulse-critical 1s infinite;
}

@keyframes pulse-warning {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes pulse-critical {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ============================================================================
   UTILITY CLASSES
   ============================================================================ */

.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }

/* ============================================================================
   ACCESSIBILITY IMPROVEMENTS
   ============================================================================ */

.option-label:focus-within {
  outline: 2px solid #1547bb;
  outline-offset: 2px;
}

.nav-btn:focus,
.submit-btn:focus,
.action-btn:focus,
.begin-btn:focus {
  outline: 2px solid #1547bb;
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .option-label {
    border-width: 3px;
  }

  .question-option input[type="radio"]:checked + .option-label {
    border-width: 4px;
  }

  .topic-item {
    border-left-width: 6px;
  }
}
