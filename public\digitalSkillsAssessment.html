<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link rel="stylesheet" type="text/css" href="digitalSkillsAssessment.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.8/lottie.min.js"></script>
    <title>Digital Skills Assessment - Skills Gap Analyzer</title>
</head>

<body class="bg-cover bg-center h-screen w-screen overflow-hidden bg-fixed flex items-center justify-center">
    <!-- Header with progress information -->
    <div id="header" class="fixed top-0 left-0 right-0 p-5 bg-blue-900 flex items-center justify-between h-12 text-white text-sm shadow-md hidden">
        <div class="header-texts flex justify-between w-full items-center">
            <h2 class="text-xs">Digital Skills Assessment</h2>
            <h2 class="text-xs">Level: <span id="current-level">EntryLevel2</span></h2>
            <h2 class="text-xs">Question <span id="current-question">1</span> of <span id="total-questions">15</span></h2>
            <h2 class="text-xs">Time: <span id="timer-display">25:00</span></h2>
        </div>
    </div>

    <!-- User Information Form -->
    <div id="user-form-container" class="hidden">
        <div class="modern-form-container">
            <div class="form-header">
                <h2>Digital Skills Assessment</h2>
                <p>Please provide your information to start your digital skills assessment</p>
            </div>

            <div class="form-content">
                <form id="user-form" class="modern-form">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">Personal Information</h3>

                        <div class="input-grid">
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="first-name"
                                    name="first-name"
                                    required
                                    class="modern-input"
                                    placeholder="First Name"
                                >
                            </div>

                            <div class="input-group">
                                <input
                                    type="text"
                                    id="last-name"
                                    name="last-name"
                                    required
                                    class="modern-input"
                                    placeholder="Last Name"
                                >
                            </div>
                        </div>

                        <div class="input-group">
                            <input
                                type="email"
                                id="email"
                                name="email"
                                required
                                class="modern-input"
                                placeholder="Email Address"
                            >
                        </div>
                    </div>

                    <!-- Assessment Level Selection -->
                    <div class="form-section">
                        <h3 class="section-title">Digital Skills Level</h3>
                        <p class="section-description">Select the digital skills level you'd like to assess</p>

                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="EntryLevel2" checked>
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Entry Level 2 - Computer Skills Beginners</span>
                                    <span class="radio-description">Basic computer parts, mouse/keyboard, simple operations (25 min, 15 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="EntryLevel2Plus">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Entry Level 2/3 - Computer Skills Beginners Plus</span>
                                    <span class="radio-description">Laptops/desktops, apps, internet safety, email (30 min, 18 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="Level1">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Level 1 - Computer Skills for Everyday Life</span>
                                    <span class="radio-description">Microsoft apps, online banking, cloud storage (35 min, 20 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="Level2">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Level 2 - Computer Skills for Work</span>
                                    <span class="radio-description">Advanced formatting, spreadsheets, presentations (40 min, 22 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="EntryLevel3">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Entry Level 3 - Improvers Plus</span>
                                    <span class="radio-description">Operating systems, email proficiency, online transactions (30 min, 16 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="ICDLLevel2">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">ICDL Level 2</span>
                                    <span class="radio-description">Timed exam format, advanced Word/Excel/PowerPoint (45 min, 25 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="ICDLLevel3">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">ICDL Level 3</span>
                                    <span class="radio-description">Advanced Microsoft applications, IT careers preparation (50 min, 30 questions)</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Student Level -->
                    <div class="form-section">
                        <h3 class="section-title">Student Level</h3>
                        <div class="input-group">
                            <select id="student-level" name="student-level" class="modern-select" required>
                                <option value="">Select your level</option>
                                <option value="adult-learner">Adult Learner</option>
                                <option value="returning-student">Returning Student</option>
                                <option value="school-leaver">School Leaver</option>
                                <option value="career-changer">Career Changer</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>

            <div class="form-footer">
                <div class="form-actions">
                    <button type="submit" form="user-form" id="submit-form" class="modern-submit-btn">
                        <span class="btn-text">Start Digital Skills Assessment</span>
                        <span class="btn-icon">→</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Digital Skills Assessment Container -->
    <div id="digital-skills-assessment-container" class="hidden">
        <!-- Assessment Instructions -->
        <div id="assessment-instructions" class="assessment-screen">
            <div class="instruction-container">
                <h2 class="instruction-title">Digital Skills Assessment Instructions</h2>
                <div class="instruction-content">
                    <div class="instruction-item">
                        <span class="instruction-icon">📝</span>
                        <p>You will have <span id="time-limit-display">25 minutes</span> to complete <span id="question-count-display">15 questions</span></p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">🎯</span>
                        <p>You need <span id="passing-score-display">12 points</span> to pass this level</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">💻</span>
                        <p>Questions cover practical digital skills and computer knowledge</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">⏰</span>
                        <p>The timer will start when you begin the assessment</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">✅</span>
                        <p>Select the best answer for each multiple-choice question</p>
                    </div>
                </div>
                
                <div class="instruction-actions">
                    <button id="begin-assessment" class="begin-btn">
                        <span class="btn-text">Begin Assessment</span>
                        <span class="btn-icon">▶</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Assessment Questions -->
        <div id="assessment-questions" class="assessment-screen hidden">
            <div class="question-container">
                <div class="question-header">
                    <div class="question-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <span class="progress-text" id="progress-text">Question 1 of 15</span>
                    </div>
                </div>

                <div class="question-content">
                    <h3 class="question-title" id="question-title">Question will appear here</h3>
                    <div class="question-options" id="question-options">
                        <!-- Options will be populated dynamically -->
                    </div>
                </div>

                <div class="question-actions">
                    <button id="prev-question" class="nav-btn prev-btn" disabled>
                        <span class="btn-icon">←</span>
                        <span class="btn-text">Previous</span>
                    </button>
                    <button id="next-question" class="nav-btn next-btn">
                        <span class="btn-text">Next</span>
                        <span class="btn-icon">→</span>
                    </button>
                    <button id="submit-assessment" class="submit-btn hidden">
                        <span class="btn-text">Submit Assessment</span>
                        <span class="btn-icon">✓</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Assessment Results -->
        <div id="assessment-results" class="assessment-screen hidden">
            <div class="results-container">
                <div class="results-header">
                    <h2 class="results-title" id="results-title">Assessment Complete!</h2>
                    <div class="results-score" id="results-score">
                        <span class="score-value">0</span>
                        <span class="score-total">/ 30</span>
                    </div>
                </div>

                <div class="results-content" id="results-content">
                    <!-- Results will be populated dynamically -->
                </div>

                <div class="results-actions">
                    <button id="view-detailed-report" class="action-btn secondary">
                        <span class="btn-text">View Detailed Report</span>
                        <span class="btn-icon">📊</span>
                    </button>
                    <button id="retake-assessment" class="action-btn secondary">
                        <span class="btn-text">Retake Assessment</span>
                        <span class="btn-icon">🔄</span>
                    </button>
                    <button id="next-level" class="action-btn primary hidden">
                        <span class="btn-text">Try Next Level</span>
                        <span class="btn-icon">⬆</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-animation" id="loading-animation"></div>
            <h3 class="loading-title" id="loading-title">Preparing Assessment...</h3>
            <p class="loading-message" id="loading-message">Please wait while we generate your questions</p>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="loading-progress-fill"></div>
                </div>
                <span class="progress-text" id="loading-progress-text">0%</span>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="validationFunctions.js"></script>
    <script src="digitalSkillsAssessment.js"></script>
    <script>
        // Initialize the digital skills assessment when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all containers are hidden initially
            document.getElementById('digital-skills-assessment-container').classList.add('hidden');
            document.getElementById('header').classList.add('hidden');

            // Show only the user form initially
            document.getElementById('user-form-container').classList.remove('hidden');

            // Initialize the assessment system
            if (typeof DigitalSkillsAssessment !== 'undefined') {
                window.digitalSkillsAssessment = new DigitalSkillsAssessment();
                window.digitalSkillsAssessment.init();
            } else {
                console.error('DigitalSkillsAssessment class not found');
            }
        });
    </script>
</body>
</html>
